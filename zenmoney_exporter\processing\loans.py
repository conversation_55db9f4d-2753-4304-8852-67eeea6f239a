from __future__ import annotations

from datetime import datetime
from typing import List, Dict, Tuple
from zenmoney_exporter.processing.organize import format_month_to_russian

# transaction type hints (trying to avoid importing zenmoney models directly here)

# loan detection helpers -------------------------------------------------

LOAN_TYPES = {"loan", "debt"}


def _loan_match(acc) -> bool:
    """return true if account object seems to be loan/debt."""
    if not acc:
        return False
    acc_type = getattr(acc, "type", "") if hasattr(acc, "type") else ""
    acc_title = getattr(acc, "title", str(acc))
    return acc_type in LOAN_TYPES or any(
        w in acc_title.lower() for w in ["займ", "долг"]
    )


def _is_loan_given(tx, accounts_by_id) -> bool:
    """money flows out to a loan/debt account (you lend)."""
    try:
        if getattr(tx, "outcome", 0) <= 0:
            return False
        income_acc = accounts_by_id.get(getattr(tx, "incomeAccount", None))
        outcome_acc = accounts_by_id.get(getattr(tx, "outcomeAccount", None))
        return _loan_match(income_acc) or _loan_match(outcome_acc)
    except Exception:
        return False


def _is_loan_taken(tx, accounts_by_id) -> bool:
    """money received from a loan/debt account (you borrow)."""
    try:
        if getattr(tx, "income", 0) <= 0:
            return False
        income_acc = accounts_by_id.get(getattr(tx, "incomeAccount", None))
        outcome_acc = accounts_by_id.get(getattr(tx, "outcomeAccount", None))
        return _loan_match(income_acc) or _loan_match(outcome_acc)
    except Exception:
        return False


def _format_tx_row(tx, given: bool) -> List:
    """return gsheet row list for a loan transaction."""
    counterparty = getattr(tx, "payee", "") or "-"
    date_ts = getattr(tx, "date", None)
    # zenmoney date is iso str? if datetime string like '2025-05-12'
    date_str = date_ts
    try:
        if isinstance(date_ts, (int, float)):
            date_str = datetime.utcfromtimestamp(int(date_ts)).strftime("%Y-%m-%d")
    except Exception:
        pass
    amount_cents = getattr(tx, "outcome", 0) if given else getattr(tx, "income", 0)
    amount = amount_cents / 100.0 if amount_cents else 0.0
    status = "активен"
    due_date = ""
    note = getattr(tx, "comment", "") or ""
    return [counterparty, date_str, amount, status, due_date, note]


HEADER = [
    "контрагент",
    "дата",
    "сумма",
    "статус",
    "срок погашения",
    "примечание",
]


def format_loans_for_gsheet(
    transactions: List, accounts_by_id: Dict
) -> Tuple[List[List], List[List]]:
    """filter loan transactions and return (loans_given_rows, loans_taken_rows) each including header."""
    loans_given_rows = [HEADER]
    loans_taken_rows = [HEADER]

    for tx in transactions:
        if _is_loan_given(tx, accounts_by_id):
            loans_given_rows.append(_format_tx_row(tx, given=True))
        elif _is_loan_taken(tx, accounts_by_id):
            loans_taken_rows.append(_format_tx_row(tx, given=False))

    # sort by date ascending (skip header)
    def sort_key(row):
        return row[1]

    loans_given_rows[1:] = sorted(loans_given_rows[1:], key=sort_key)
    loans_taken_rows[1:] = sorted(loans_taken_rows[1:], key=sort_key)

    return loans_given_rows, loans_taken_rows


def organize_loans_by_month(
    loans_given_rows: List[List], loans_taken_rows: List[List]
) -> Dict[str, Dict[str, List[List]]]:
    """group loan rows by russian MONTH-YYYY format similar to existing helpers."""
    from collections import defaultdict
    import locale

    (
        locale.setlocale(locale.LC_TIME, "ru_RU.UTF-8")
        if hasattr(locale, "LC_TIME")
        else None
    )

    def month_key(date_str: str) -> str:
        try:
            year_month = date_str[:7]
            return format_month_to_russian(year_month)
        except Exception:
            return "unknown"

    grouped: Dict[str, Dict[str, List[List]]] = defaultdict(
        lambda: {"given": [HEADER], "taken": [HEADER]}
    )

    for row in loans_given_rows[1:]:
        key = month_key(row[1])
        grouped[key]["given"].append(row)

    for row in loans_taken_rows[1:]:
        key = month_key(row[1])
        grouped[key]["taken"].append(row)

    return grouped
