import gspread
from loguru import logger
from datetime import datetime, timedelta
from zenmoney import Request, Diff
from zenmoney.exception import ZenMoneyEx<PERSON>
from gspread.exceptions import SpreadsheetNotFound, WorksheetNotFound
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import sys
import os
import yaml
from collections import defaultdict

# setup better logging with loguru - colorful like new bathroom tiles, not old grey ones!
logger.remove()
logger.add(
    sink=sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> - <level>{level}</level> - <cyan>{message}</cyan>",
    colorize=True,
    level="INFO",
)


def load_config():
    """
    load configuration from yaml file.
    if pipe blueprint (config) missing, we cannot work!
    """
    config_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)) + "/credentials/config.yaml"
    )

    # check if config file exists
    if not os.path.exists(config_path):
        logger.error("pizdec! config file not found at: {}".format(config_path))
        logger.error("please create config.yaml file from example or run setup")
        return None

    # load config from yaml file
    try:
        with open(config_path, "r") as file:
            return yaml.safe_load(file)
    except yaml.YAMLError as e:
        logger.error(f"blyat! error parsing config.yaml: {e}")
        return None
    except Exception as e:
        logger.error(f"unknown error loading config: {e}")
        return None


# load configuration
CONFIG = load_config()
if not CONFIG:
    logger.error("cannot continue without proper configuration! exiting...")
    sys.exit(1)

# set log level from config
# ensure the value from config is used as is for level, but if it were hardcoded, it'd be lowercase.
# the initial logger.add above already sets a default, this reconfigures it.
logger.remove()
logger.add(
    sink=sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> - <level>{level}</level> - <cyan>{message}</cyan>",
    colorize=True,
    level=CONFIG["settings"][
        "log_level"
    ].upper(),  # loguru expects uppercase log levels
)


def get_zenmoney_transactions(days_back, fetch_all_tags=True):
    """
    connects to zenmoney and fetches recent transactions.
    is like opening valve and seeing what comes out.

    args:
        days_back: number of days to fetch transactions for
        fetch_all_tags: if true, makes a separate request to fetch all historical tags
    """
    logger.info("connecting to zenmoney api...")
    try:
        api = Request(CONFIG["zenmoney"]["api_key"])
        logger.info("successfully authenticated with zenmoney.")
    except Exception as e:
        logger.error(f"blyat! failed to authenticate with zenmoney: {e}")
        return None, None, None  # return nothing if connection fails

    # calculate timestamp for fetching recent data
    # like setting timer on water meter
    now = datetime.now()
    start_date = now - timedelta(days=days_back)
    # zenmoney uses unix timestamp, is like measuring pipe pressure in old units
    server_timestamp = int(start_date.timestamp())
    logger.info(
        f'fetching data since timestamp: {server_timestamp} ({start_date.strftime("%y-%m-%d")})'
    )

    try:
        # this 'diff' is like checking water flow difference
        # we ask for everything since our timestamp
        # first sync might need servertimestamp=1 to get *everything*, but that's a big job, like replacing main pipe
        diff_request = Diff(serverTimestamp=server_timestamp)
        diff_response = api.diff(diff_request)
        logger.info(
            f"received diff from server. new server timestamp: {diff_response.serverTimestamp}"
        )

        # extract transactions, accounts - the important bits, like finding right fittings
        transactions = getattr(diff_response, "transaction", [])
        accounts = {acc.id: acc.title for acc in getattr(diff_response, "account", [])}

        # get tags from the recent diff response first
        tags = {tag.id: tag.title for tag in getattr(diff_response, "tag", [])}

        # to get all tags (including historical ones), make a separate request with timestamp=1
        if fetch_all_tags:
            logger.info("fetching all historical tags from zenmoney...")
            all_tags_request = Diff(serverTimestamp=1)
            all_tags_response = api.diff(all_tags_request)

            # extract all tags from response and merge with existing tags
            all_tags_data = {
                tag.id: tag.title for tag in getattr(all_tags_response, "tag", [])
            }
            tags.update(
                all_tags_data  # renamed to avoid conflict with outer scope 'tags' if it was a simple variable
            )  # update will preserve existing keys if there are duplicates

        logger.info(f"fetched {len(transactions)} transactions.")
        logger.info(f"accounts: {accounts}")  # content of accounts will be as is
        logger.info(f"tags: {len(tags)} tags found")

        # make transactions more readable in logs like clean water through clear pipe
        readable_transactions = []
        for t in transactions[:3] if transactions else []:
            readable_tx = {
                "id": getattr(t, "id", "unknown"),
                "date": getattr(t, "date", "unknown"),
                "income": getattr(t, "income", 0),
                "outcome": getattr(t, "outcome", 0),
                "payee": getattr(t, "payee", ""),
                "comment": getattr(t, "comment", ""),
            }
            readable_transactions.append(readable_tx)

        logger.info(
            f"transactions sample: {readable_transactions}..."
        )  # content of readable_transactions will be as is
        return transactions, accounts, tags

    except ZenMoneyException as e:
        logger.error(f"pizdec! zenmoney api error: {e}")
        return None, None, None
    except Exception as e:
        logger.error(f"unknown error fetching zenmoney data: {e}")
        return None, None, None


def format_transactions_for_gsheet(transactions, accounts, tags):
    """
    takes raw sewage (data) from zenmoney and makes it look nice for spreadsheet.
    like cleaning pipes before installing fancy faucet.
    """
    if not transactions:
        return []

    header = [
        "date",
        "type",
        "income_account",
        "income",
        "outcome_account",
        "outcome",
        "payee",
        "comment",
        "tags",
        "transaction_id",
        "created_at",
        "changed_at",
    ]  # these are identifiers, kept as is (already lowercase)
    formatted_data = [header]

    for t in transactions:
        # skip deleted transactions, is like ignoring plugged drain
        if getattr(t, "deleted", False):
            continue

        # determine transaction type (income, outcome, transfer)
        tx_type = "unknown"  # internal status string, kept as is
        if t.income > 0 and t.outcome == 0:
            tx_type = "income"
        elif t.outcome > 0 and t.income == 0:
            tx_type = "outcome"
        elif t.income > 0 and t.outcome > 0:
            tx_type = "transfer"
        elif getattr(t, "opIncome", 0) > 0 or getattr(t, "opOutcome", 0) > 0:
            tx_type = "currency_exchange"  # or something similar

        # get account names, handle if account not found (maybe old data?)
        income_acc_name = accounts.get(
            t.incomeAccount,
            f"unknown_acc_{t.incomeAccount[:4]}",  # "unknown_acc_" is part of data, kept as is (already lc)
        )
        outcome_acc_name = accounts.get(
            t.outcomeAccount,
            f"unknown_acc_{t.outcomeAccount[:4]}",  # "unknown_acc_" is part of data, kept as is (already lc)
        )

        # get tag names, join if multiple
        tag_ids = getattr(t, "tag", []) or []
        tag_names = ", ".join(
            [
                tags.get(tag_id, f"unknown_tag_{tag_id[:4]}") for tag_id in tag_ids
            ]  # "unknown_tag_" is part of data, kept as is (already lc)
        )

        # format timestamps, is like reading old water meter dials
        created_dt = (
            datetime.fromtimestamp(t.created).strftime(
                "%y-%m-%d %H:%M:%S"
            )  # time format specifiers kept as is
            if hasattr(t, "created")
            else ""
        )
        changed_dt = (
            datetime.fromtimestamp(t.changed).strftime(
                "%y-%m-%d %H:%M:%S"
            )  # time format specifiers kept as is
            if hasattr(t, "changed")
            else ""
        )

        row = [
            getattr(t, "date", ""),
            tx_type,
            income_acc_name,
            t.income,
            outcome_acc_name,
            t.outcome,
            getattr(t, "payee", ""),
            getattr(t, "comment", ""),
            tag_names,
            t.id,  # unique id, like serial number on pipe fitting
            created_dt,
            changed_dt,
        ]
        formatted_data.append(row)

    logger.info(f"formatted {len(formatted_data) - 1} transactions for google sheets.")
    return formatted_data


def update_google_sheet(data):
    """
    pushes the clean water (data) into the google sheets tank.
    uses the gspread pipe.
    """
    if not data or len(data) <= 1:  # check if only header is present
        logger.info("no new transaction data to upload.")
        return

    # get google sheets config
    google_creds_path = CONFIG["google_sheets"]["credentials_path"]
    spreadsheet_key = CONFIG["google_sheets"]["spreadsheet_key"]
    worksheet_name_config = CONFIG["google_sheets"][
        "worksheet_name"
    ]  # renamed to avoid conflict

    logger.info(
        f'connecting to google sheets api using service account: {google_creds_path or "default path"}...'
    )
    try:
        if google_creds_path:
            gc = gspread.service_account(filename=google_creds_path)
        else:
            # try default paths
            gc = gspread.service_account()
        logger.info("successfully authenticated with google sheets.")

        # open the spreadsheet (tank)
        try:
            # use more reliable open_by_key method - like using exact pipe size, not "whatever fits"
            sh = gc.open_by_key(spreadsheet_key)
            logger.info(
                f'opened spreadsheet by key: "{sh.title}"'
            )  # sh.title is external, kept as is
        except SpreadsheetNotFound:
            logger.error(
                f'blyat! spreadsheet with key "{spreadsheet_key}" not found. check key and sharing permissions.'
            )
            return
        except Exception as e:
            logger.error(f"pizdec! could not open spreadsheet: {str(e)}")
            # show full error information, like taking photo of broken pipe to show plumber friend
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")
            return

        # select or create worksheet (specific compartment in tank)
        try:
            worksheet = sh.worksheet(worksheet_name_config)
            logger.info(f'found existing worksheet: "{worksheet_name_config}"')
            # clear existing data before writing new data? maybe better to append or find last row?
            # for simplicity, we clear and write. is like flushing tank before refill.
            try:
                worksheet.clear()
                logger.info("cleared existing data in worksheet.")
            except Exception as e:
                logger.error(f"blyat! could not clear worksheet: {str(e)}")
                logger.error(
                    "this usually means service account does not have edit permission, only view."
                )
                logger.error(
                    "need to share spreadsheet with service account email with edit permission!"
                )
                # extract service account email from credentials if possible
                try:
                    import json

                    with open(google_creds_path) as f:
                        creds = json.load(f)
                        service_account_email = creds.get(
                            "client_email", "unknown"
                        )  # "unknown" is data, kept lc
                        logger.info(
                            f"your service account email is: {service_account_email}"
                        )
                        logger.info(
                            'share spreadsheet with this email and give "editor" permission'  # "editor" is a role, but here part of user message, so lc
                        )
                except Exception:
                    logger.info(
                        "could not read service account email from credentials. check your service_account.json file"
                    )
                return
        except WorksheetNotFound:
            logger.info(
                f'worksheet "{worksheet_name_config}" not found, creating new one...'
            )
            # estimate rows/cols needed. add some buffer, like using slightly bigger pipe.
            rows_needed = len(data) + 10
            cols_needed = (
                len(data[0]) + 2 if data else 20
            )  # use header length if available
            try:
                worksheet = sh.add_worksheet(
                    title=worksheet_name_config, rows=rows_needed, cols=cols_needed
                )
                logger.info(f'created new worksheet: "{worksheet_name_config}"')
            except Exception as e:
                logger.error(f"pizdec! could not create worksheet: {str(e)}")
                logger.error(
                    "this usually means service account does not have edit permission, only view. need to share spreadsheet with service account email with edit permission!"
                )
                # extract service account email from credentials if possible
                try:
                    import json

                    with open(google_creds_path) as f:
                        creds = json.load(f)
                        service_account_email = creds.get("client_email", "unknown")
                        logger.info(
                            f"your service account email is: {service_account_email}"
                        )
                except Exception:
                    logger.info(
                        "could not read service account email from credentials. check your service_account.json file"
                    )
                return
        except Exception as e:
            logger.error(f"pizdec! could not get or create worksheet: {str(e)}")
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")
            return

        # write data to the worksheet (fill the tank)
        logger.info(f"writing {len(data)} rows (including header) to worksheet...")
        worksheet.update(data, "a1")  # start writing from cell a1

        # maybe add some basic formatting, like painting the pipe blue
        header_format = {"textFormat": {"bold": True}}  # api keys, kept as is
        worksheet.format(
            f"A1:{chr(ord('A') + len(data[0]) - 1)}1", header_format
        )  # cell range, kept as is
        logger.info("applied basic formatting to header row.")

        logger.info("successfully updated google sheet.")

        # set up the sheets api for chart creation
        try:
            from google.oauth2 import service_account

            SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]  # url, kept as is

            # load credentials from the same service account file
            creds = service_account.Credentials.from_service_account_file(
                google_creds_path, scopes=SCOPES
            )
            sheets_service = build("sheets", "v4", credentials=creds)
            logger.info("initialized sheets api service for chart creation")

            # get the sheet id for this worksheet
            sheet_id = worksheet._properties["sheetId"]  # api property, kept as is

            # add charts to the general worksheet
            add_general_charts(
                sheets_service, spreadsheet_key, worksheet_name_config, sheet_id, data
            )

        except Exception as e:
            logger.error(f"blyat! failed to add charts to general sheet: {e}")
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")

    except Exception as e:
        logger.error(f"blyat! failed to update google sheet: {e}")


def add_general_charts(service, spreadsheet_key, worksheet_name, sheet_id, data):
    """
    adds income/outcome line chart and categories pie chart to the general worksheet.
    like adding main flow meters to the main tank.
    """
    if not data or len(data) <= 1:
        logger.info(f"not enough data to create charts for {worksheet_name}")
        return

    try:
        # define column indices for the data we need
        date_col = 0  # column a: date
        income_col = 3  # column d: income
        outcome_col = 5  # column f: outcome
        tags_col = 8  # column i: tags

        # need to add padding at the top of the general sheet for charts
        # create padding request - 20 rows at the top
        padding_request = {
            "insertRange": {  # api key
                "range": {  # api key
                    "sheetId": sheet_id,
                    "startRowIndex": 0,
                    "endRowIndex": 20,
                    "startColumnIndex": 0,
                    "endColumnIndex": len(data[0]),
                },
                "shiftDimension": "ROWS",  # api enum value
            }
        }

        # execute the padding request
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key,
            body={"requests": [padding_request]},
        ).execute()
        logger.info(f"added padding rows at the top of {worksheet_name}")

        # move data to row 21 (after padding)
        # first, clear the data in the new position (in case there's something there)
        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A21:Z{21+len(data)}",  # range string
        ).execute()

        # now move the data from a1 to a21
        service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A21",  # range string
            valueInputOption="RAW",  # api enum value
            body={"values": data},
        ).execute()
        logger.info(f"moved data to row 21 in {worksheet_name}")

        # clear the original data position
        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A1:Z20",  # range string
        ).execute()

        # data now starts at row 21 (after padding)
        data_start_row = 20  # 0-indexed

        # prepare to create charts
        charts_request_body = {"requests": []}  # renamed to avoid conflict

        # adjust column widths based on content
        column_widths = [
            {"column": 0, "width": 90},  # date (a) - 10 chars
            {"column": 1, "width": 70},  # type (b) - 7 chars
            {"column": 2, "width": 120},  # income_account (c) - 12-15 chars
            {"column": 3, "width": 70},  # income (d) - 5-6 digits
            {"column": 4, "width": 120},  # outcome_account (e) - 12-15 chars
            {"column": 5, "width": 70},  # outcome (f) - 6 digits
            {"column": 6, "width": 250},  # payee (g) - 30-35 chars
            {"column": 7, "width": 100},  # comment (h) - variable
            {"column": 8, "width": 100},  # tags (i) - 10-15 chars
            {"column": 9, "width": 280},  # transaction_id (j) - 36 chars (uuid)
            {"column": 10, "width": 130},  # created_at (k) - 17 chars
            {"column": 11, "width": 130},  # changed_at (l) - 17 chars
        ]

        # create column width adjustment requests
        column_requests = []
        for col_def in column_widths:
            column_requests.append(
                {
                    "updateDimensionProperties": {  # api key
                        "range": {  # api key
                            "sheetId": sheet_id,
                            "dimension": "COLUMNS",  # api enum value
                            "startIndex": col_def["column"],
                            "endIndex": col_def["column"] + 1,
                        },
                        "properties": {"pixelSize": col_def["width"]},  # api key
                        "fields": "pixelSize",  # api key
                    }
                }
            )

        # execute column width adjustments
        if column_requests:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={"requests": column_requests},
            ).execute()
            logger.info(f"adjusted column widths in {worksheet_name} based on content")

        # validate that we have sufficient numeric data for the line chart
        valid_data_rows = 0
        for row in data[1:]:  # skip header
            try:
                # check if income and outcome are valid numbers
                # income_val = float(row[income_col]) if row[income_col] != "" else 0 # renamed
                # outcome_val = float(row[outcome_col]) if row[outcome_col] != "" else 0 # renamed
                valid_data_rows += 1
            except (ValueError, TypeError, IndexError):
                # skip rows with invalid data
                continue

        # define chart dimensions for proper proportions
        line_chart_width = 800
        line_chart_height = 350
        pie_chart_width = 400  # 50% of line chart width
        pie_chart_height = 350

        # only create line chart if we have enough valid data rows
        if valid_data_rows >= 2:  # need at least 2 data points for a meaningful chart
            # 1. line chart for income and outcome over time
            line_chart = {
                "addChart": {  # api key
                    "chart": {  # api key
                        "spec": {  # api key
                            "title": f"income vs outcome (all transactions)",  # chart title, lc
                            "basicChart": {  # api key
                                "chartType": "LINE",  # api enum value
                                "legendPosition": "BOTTOM_LEGEND",  # api enum value
                                "axis": [  # api key
                                    {
                                        "position": "BOTTOM_AXIS",  # api enum value
                                        "title": "date",  # axis title, lc
                                    },
                                    {
                                        "position": "LEFT_AXIS",  # api enum value
                                        "title": "amount",  # axis title, lc
                                    },
                                ],
                                "domains": [  # api key
                                    {
                                        "domain": {  # api key
                                            "sourceRange": {  # api key
                                                "sources": [  # api key
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": date_col,
                                                        "endColumnIndex": date_col + 1,
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                "series": [  # api key
                                    {
                                        "series": {  # api key
                                            "sourceRange": {  # api key
                                                "sources": [  # api key
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": income_col,
                                                        "endColumnIndex": income_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",  # api enum value
                                        "color": {
                                            "red": 0.2,
                                            "green": 0.6,
                                            "blue": 0.2,
                                        },  # api key
                                    },
                                    {
                                        "series": {  # api key
                                            "sourceRange": {  # api key
                                                "sources": [  # api key
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": outcome_col,
                                                        "endColumnIndex": outcome_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",  # api enum value
                                        "color": {
                                            "red": 0.8,
                                            "green": 0.2,
                                            "blue": 0.2,
                                        },  # api key
                                    },
                                ],
                                "headerCount": 1,  # api key
                            },
                        },
                        "position": {  # api key
                            "overlayPosition": {  # api key
                                "anchorCell": {  # api key
                                    "sheetId": sheet_id,
                                    "rowIndex": 1,
                                    "columnIndex": 0,
                                },
                                "widthPixels": line_chart_width,  # api key
                                "heightPixels": line_chart_height,  # api key
                            }
                        },
                    }
                }
            }
            charts_request_body["requests"].append(line_chart)
        else:
            logger.info(
                f"skipping line chart for {worksheet_name}: insufficient numeric data (found {valid_data_rows} valid rows)"
            )

        # 2. aggregate transactions by tags for pie chart
        # first we need to count/sum by category
        tags_aggregation_data = {}  # renamed
        # total_outcome_val = 0 # renamed
        valid_tag_rows = 0

        for row in data[1:]:  # skip header
            if not row[tags_col]:  # skip entries with no tags
                continue

            try:
                # handle multiple tags (comma-separated)
                row_tags = row[tags_col].split(", ")
                outcome_val = float(row[outcome_col]) if row[outcome_col] else 0

                if outcome_val <= 0:  # only include expenses
                    continue

                # total_outcome_val += outcome_val
                valid_tag_rows += 1

                # split the outcome evenly between multiple tags
                tag_value = outcome_val / len(row_tags)

                for tag_item in row_tags:  # renamed
                    if tag_item in tags_aggregation_data:
                        tags_aggregation_data[tag_item] += tag_value
                    else:
                        tags_aggregation_data[tag_item] = tag_value
            except (ValueError, TypeError, IndexError):
                # skip rows with invalid data
                continue

        # filter to top 5 categories for readability
        top_tags = sorted(
            tags_aggregation_data.items(), key=lambda x: x[1], reverse=True
        )[:5]

        # create a separate area for pie chart data at the top of the sheet
        # use a fixed area for consistency, starting at row 2, column 13 (m)
        pie_data_start_row = 1
        pie_data_col = 12  # column m

        # clear the area regardless of whether we'll create a chart
        # this ensures we don't have leftover data from previous runs
        clear_request = {
            "updateCells": {  # api key
                "range": {  # api key
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 10,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "fields": "userEnteredValue",  # api key
            }
        }
        charts_request_body["requests"].append(clear_request)

        # check if we have enough valid tag data for a real pie chart
        has_enough_data = valid_tag_rows >= 2 and len(top_tags) >= 1

        # always add the header row
        chart_data_header_req = {  # renamed
            "updateCells": {  # api key
                "range": {  # api key
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 1,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "rows": [  # api key
                    {
                        "values": [  # api key
                            {
                                "userEnteredValue": {"stringValue": "category"}
                            },  # header, lc
                            {
                                "userEnteredValue": {"stringValue": "amount"}
                            },  # header, lc
                        ]
                    }
                ],
                "fields": "userEnteredValue",  # api key
            }
        }
        charts_request_body["requests"].append(chart_data_header_req)

        # if we have enough data, use real tag data
        if has_enough_data:
            # add data rows for categories
            chart_data_values = []
            for tag_item, amount_val in top_tags:  # renamed
                chart_data_values.append(
                    {
                        "values": [  # api key
                            {
                                "userEnteredValue": {"stringValue": tag_item}
                            },  # data, kept as is
                            {
                                "userEnteredValue": {"numberValue": amount_val}
                            },  # data, kept as is
                        ]
                    }
                )

            # add category data
            chart_data_values_req = {  # renamed
                "updateCells": {  # api key
                    "range": {  # api key
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 1 + len(top_tags),
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": chart_data_values,  # api key
                    "fields": "userEnteredValue",  # api key
                }
            }
            charts_request_body["requests"].append(chart_data_values_req)
        else:
            # add placeholder data when there's not enough real data
            # this ensures the chart can be created without errors
            logger.info(f"using placeholder data for pie chart in {worksheet_name}")
            placeholder_data = [
                {
                    "values": [  # api key
                        {
                            "userEnteredValue": {"stringValue": "no data"}
                        },  # placeholder, lc
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
                {
                    "values": [  # api key
                        {
                            "userEnteredValue": {"stringValue": "add transactions"}
                        },  # placeholder, lc
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
            ]

            placeholder_request = {
                "updateCells": {  # api key
                    "range": {  # api key
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 3,
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": placeholder_data,  # api key
                    "fields": "userEnteredValue",  # api key
                }
            }
            charts_request_body["requests"].append(placeholder_request)

        # always create the pie chart (with either real or placeholder data)
        pie_chart_title = "top expense categories (all transactions)"  # title, lc
        if not has_enough_data:
            pie_chart_title += " (not enough data)"  # title part, lc

        pie_chart = {
            "addChart": {  # api key
                "chart": {  # api key
                    "spec": {  # api key
                        "title": pie_chart_title,  # use var
                        "pieChart": {  # api key
                            "legendPosition": "RIGHT_LEGEND",  # api enum value
                            "domain": {  # api key
                                "sourceRange": {  # api key
                                    "sources": [  # api key
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col,
                                            "endColumnIndex": pie_data_col + 1,
                                        }
                                    ]
                                }
                            },
                            "series": {  # api key
                                "sourceRange": {  # api key
                                    "sources": [  # api key
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col + 1,
                                            "endColumnIndex": pie_data_col + 2,
                                        }
                                    ]
                                }
                            },
                            "pieHole": 0.3,  # creates a donut chart for better visibility
                        },
                    },
                    "position": {  # api key
                        "overlayPosition": {  # api key
                            "anchorCell": {  # api key
                                "sheetId": sheet_id,
                                "rowIndex": 1,
                                "columnIndex": 8,
                            },
                            "widthPixels": pie_chart_width,  # api key
                            "heightPixels": pie_chart_height,  # api key
                        }
                    },
                }
            }
        }
        charts_request_body["requests"].append(pie_chart)

        # execute the batch update to add the charts
        if charts_request_body["requests"]:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=charts_request_body
            ).execute()
            logger.info(f"successfully added charts to {worksheet_name}")

        # re-apply header formatting after moving data
        header_format_request = {
            "repeatCell": {  # api key
                "range": {  # api key
                    "sheetId": sheet_id,
                    "startRowIndex": data_start_row,
                    "endRowIndex": data_start_row + 1,
                    "startColumnIndex": 0,
                    "endColumnIndex": len(data[0]),
                },
                "cell": {  # api key
                    "userEnteredFormat": {  # api key
                        "textFormat": {"bold": True}  # api key
                    }
                },
                "fields": "userEnteredFormat.textFormat.bold",  # api key
            }
        }
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key,
            body={"requests": [header_format_request]},
        ).execute()
        logger.info(f"reapplied header formatting in {worksheet_name}")

    except Exception as e:
        logger.error(f"pizdec! failed to add charts to {worksheet_name}: {e}")
        import traceback

        logger.error(f"detailed error: {traceback.format_exc()}")


def add_demo_charts(spreadsheet_key, credentials_path):
    """
    adds demo charts to a new sheet in the google spreadsheet.
    like installing fancy water pressure gauges to see flow.
    """
    logger.info("preparing to add demo charts to google sheet...")

    try:
        # authenticate with google sheets api - need to use service account like main pipe connector
        from google.oauth2 import service_account

        # define scopes - we need full access to create charts
        SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]  # url, kept as is

        # load credentials from the same service account file
        try:
            creds = service_account.Credentials.from_service_account_file(
                credentials_path, scopes=SCOPES
            )
            logger.info("loaded service account credentials for charts api")
        except Exception as e:
            logger.error(f"could not load service account credentials: {e}")
            return

        # build the sheets api service - different pipe than gspread
        service = build("sheets", "v4", credentials=creds)

        # create a new sheet for our demo charts
        # like adding a new tank section for fancy displays
        sheet_title = (
            f"demo_charts_{datetime.now().strftime('%m%d')}"  # "demo_charts" already lc
        )

        try:
            # create new sheet
            add_sheet_request_body = {  # renamed
                "requests": [
                    {"addSheet": {"properties": {"title": sheet_title}}}
                ]  # api keys
            }

            sheet_response = (
                service.spreadsheets()
                .batchUpdate(spreadsheetId=spreadsheet_key, body=add_sheet_request_body)
                .execute()
            )

            # extract the new sheet id - important for chart placement
            new_sheet_id = sheet_response["replies"][0]["addSheet"]["properties"][
                "sheetId"
            ]  # api keys
            logger.info(f"created new sheet '{sheet_title}' with id {new_sheet_id}")

            # add sample data for our charts - like test pressure readings
            sample_data = [
                ["month", "income", "expenses", "savings"],  # headers, lc
                ["january", 50000, 30000, 20000],  # data labels, lc
                ["february", 55000, 25000, 30000],  # data labels, lc
                ["march", 52000, 28000, 24000],  # data labels, lc
                ["april", 58000, 32000, 26000],  # data labels, lc
                ["may", 53000, 29000, 24000],  # data labels, lc
                ["june", 60000, 35000, 25000],  # data labels, lc
            ]

            # update the values in the new sheet
            service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_key,
                range=f"{sheet_title}!A1",  # range string
                valueInputOption="USER_ENTERED",  # api enum value
                body={"values": sample_data},
            ).execute()
            logger.info("added sample data to demo charts sheet")

            # now add charts
            # create column chart - like vertical pipe pressure gauge
            column_chart_request_body = {  # renamed
                "requests": [  # api key
                    {
                        "addChart": {  # api key
                            "chart": {  # api key
                                "spec": {  # api key
                                    "title": "monthly income vs expenses",  # title, lc
                                    "basicChart": {  # api key
                                        "chartType": "COLUMN",  # api enum value
                                        "legendPosition": "BOTTOM_LEGEND",  # api enum value
                                        "axis": [  # api key
                                            {
                                                "position": "BOTTOM_AXIS",  # api enum value
                                                "title": "month",  # axis title, lc
                                            },
                                            {
                                                "position": "LEFT_AXIS",  # api enum value
                                                "title": "amount",  # axis title, lc
                                            },
                                        ],
                                        "domains": [  # api key
                                            {
                                                "domain": {  # api key
                                                    "sourceRange": {  # api key
                                                        "sources": [  # api key
                                                            {
                                                                "sheetId": new_sheet_id,
                                                                "startRowIndex": 0,
                                                                "endRowIndex": 7,
                                                                "startColumnIndex": 0,
                                                                "endColumnIndex": 1,
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        ],
                                        "series": [  # api key
                                            {
                                                "series": {  # api key
                                                    "sourceRange": {  # api key
                                                        "sources": [  # api key
                                                            {
                                                                "sheetId": new_sheet_id,
                                                                "startRowIndex": 0,
                                                                "endRowIndex": 7,
                                                                "startColumnIndex": 1,
                                                                "endColumnIndex": 2,
                                                            }
                                                        ]
                                                    }
                                                },
                                                "targetAxis": "LEFT_AXIS",  # api enum value
                                            },
                                            {
                                                "series": {  # api key
                                                    "sourceRange": {  # api key
                                                        "sources": [  # api key
                                                            {
                                                                "sheetId": new_sheet_id,
                                                                "startRowIndex": 0,
                                                                "endRowIndex": 7,
                                                                "startColumnIndex": 2,
                                                                "endColumnIndex": 3,
                                                            }
                                                        ]
                                                    }
                                                },
                                                "targetAxis": "LEFT_AXIS",  # api enum value
                                            },
                                        ],
                                        "headerCount": 1,  # api key
                                    },
                                },
                                "position": {  # api key
                                    "overlayPosition": {  # api key
                                        "anchorCell": {  # api key
                                            "sheetId": new_sheet_id,
                                            "rowIndex": 0,
                                            "columnIndex": 5,
                                        }
                                    }
                                },
                            }
                        }
                    }
                ]
            }

            # add column chart to sheet
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=column_chart_request_body
            ).execute()
            logger.info("added column chart to demo sheet")

            # create a pie chart - like pie-shaped flow meter
            pie_chart_request_body = {  # renamed
                "requests": [  # api key
                    {
                        "addChart": {  # api key
                            "chart": {  # api key
                                "spec": {  # api key
                                    "title": "average monthly distribution",  # title, lc
                                    "pieChart": {  # api key
                                        "legendPosition": "RIGHT_LEGEND",  # api enum value
                                        "domain": {  # api key
                                            "sourceRange": {  # api key
                                                "sources": [  # api key
                                                    {
                                                        "sheetId": new_sheet_id,
                                                        "startRowIndex": 0,
                                                        "endRowIndex": 1,
                                                        "startColumnIndex": 1,
                                                        "endColumnIndex": 4,
                                                    }
                                                ]
                                            }
                                        },
                                        "series": {  # api key
                                            "sourceRange": {  # api key
                                                "sources": [  # api key
                                                    {
                                                        "sheetId": new_sheet_id,
                                                        "startRowIndex": 6,  # this might be an error in original, should be 1 for values if header is row 0
                                                        "endRowIndex": 7,  # and data is in row 1. for demo, using as is.
                                                        "startColumnIndex": 1,
                                                        "endColumnIndex": 4,
                                                    }
                                                ]
                                            }
                                        },
                                    },
                                },
                                "position": {  # api key
                                    "overlayPosition": {  # api key
                                        "anchorCell": {  # api key
                                            "sheetId": new_sheet_id,
                                            "rowIndex": 0,
                                            "columnIndex": 5,
                                        },
                                        "offsetXPixels": 0,  # api key
                                        "offsetYPixels": 300,  # api key
                                    }
                                },
                            }
                        }
                    }
                ]
            }

            # add pie chart to sheet
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=pie_chart_request_body
            ).execute()
            logger.info("added pie chart to demo sheet")

            # create a line chart - like flow trend gauge
            line_chart_request_body = {  # renamed
                "requests": [  # api key
                    {
                        "addChart": {  # api key
                            "chart": {  # api key
                                "spec": {  # api key
                                    "title": "savings trend",  # title, lc
                                    "basicChart": {  # api key
                                        "chartType": "LINE",  # api enum value
                                        "legendPosition": "BOTTOM_LEGEND",  # api enum value
                                        "axis": [  # api key
                                            {
                                                "position": "BOTTOM_AXIS",  # api enum value
                                                "title": "month",  # axis title, lc
                                            },
                                            {
                                                "position": "LEFT_AXIS",  # api enum value
                                                "title": "amount",  # axis title, lc
                                            },
                                        ],
                                        "domains": [  # api key
                                            {
                                                "domain": {  # api key
                                                    "sourceRange": {  # api key
                                                        "sources": [  # api key
                                                            {
                                                                "sheetId": new_sheet_id,
                                                                "startRowIndex": 0,
                                                                "endRowIndex": 7,
                                                                "startColumnIndex": 0,
                                                                "endColumnIndex": 1,
                                                            }
                                                        ]
                                                    }
                                                }
                                            }
                                        ],
                                        "series": [  # api key
                                            {
                                                "series": {  # api key
                                                    "sourceRange": {  # api key
                                                        "sources": [  # api key
                                                            {
                                                                "sheetId": new_sheet_id,
                                                                "startRowIndex": 0,
                                                                "endRowIndex": 7,
                                                                "startColumnIndex": 3,
                                                                "endColumnIndex": 4,
                                                            }
                                                        ]
                                                    }
                                                },
                                                "targetAxis": "LEFT_AXIS",  # api enum value
                                            }
                                        ],
                                        "headerCount": 1,  # api key
                                    },
                                },
                                "position": {  # api key
                                    "overlayPosition": {  # api key
                                        "anchorCell": {  # api key
                                            "sheetId": new_sheet_id,
                                            "rowIndex": 15,
                                            "columnIndex": 5,
                                        }
                                    }
                                },
                            }
                        }
                    }
                ]
            }

            # add line chart to sheet
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=line_chart_request_body
            ).execute()
            logger.info("added line chart to demo sheet")

            logger.info(f"successfully added all demo charts to sheet '{sheet_title}'")

        except HttpError as error:
            logger.error(f"pizdec! api error when creating charts: {error}")
        except Exception as e:
            logger.error(f"blyat! failed to create demo charts: {e}")
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")

    except Exception as e:
        logger.error(f"pizdec! failed to set up charts api: {e}")


def organize_transactions_by_month(data):
    """
    organizes transaction data by month into separate sheets.
    like sorting water flow into different tanks by temperature.
    """
    if not data or len(data) <= 1:  # check if only header is present
        logger.info("no transaction data to organize by month.")
        return {}

    header = data[0]
    transactions_by_month = defaultdict(list)

    # add header to each month's data
    # date is in the first column (index 0)
    date_index = 0

    # group transactions by month
    for row in data[1:]:  # skip header row
        try:
            # parse date from row (format is yyyy-mm-dd)
            date_str = row[date_index]
            # extract year and month for grouping
            if date_str and len(date_str) >= 7:
                year_month = date_str[:7]  # extract yyyy-mm
                transactions_by_month[year_month].append(row)
            else:
                # if date format is unexpected, put in 'unknown' group
                transactions_by_month["unknown"].append(
                    row
                )  # "unknown" is key, kept as is
        except Exception as e:
            logger.error(f"error parsing date for row: {row}: {e}")
            transactions_by_month["unknown"].append(row)

    # add header to each month's data
    for month, month_transactions in transactions_by_month.items():  # renamed
        transactions_by_month[month] = [header] + month_transactions

    logger.info(
        f"organized transactions into {len(transactions_by_month)} monthly groups"
    )
    return transactions_by_month


def add_monthly_charts(service, spreadsheet_key, worksheet_name, sheet_id, data):
    """
    adds income/outcome line chart and categories pie chart to a monthly worksheet.
    like adding flow meters to each tank section.
    charts are positioned above the data table in the padding area.
    """
    if not data or len(data) <= 1:
        logger.info(f"not enough data to create charts for {worksheet_name}")
        return

    try:
        # define column indices for the data we need
        date_col = 0  # column a: date
        income_col = 3  # column d: income
        outcome_col = 5  # column f: outcome
        tags_col = 8  # column i: tags

        # data starts at row 21 (after padding)
        data_start_row = 20

        # prepare to create charts
        charts_request_body = {"requests": []}  # renamed

        # validate that we have sufficient numeric data for the line chart
        valid_data_rows = 0
        for row in data[1:]:  # skip header
            try:
                # check if income and outcome are valid numbers
                # income_val = float(row[income_col]) if row[income_col] != "" else 0 # renamed
                # outcome_val = float(row[outcome_col]) if row[outcome_col] != "" else 0 # renamed
                valid_data_rows += 1
            except (ValueError, TypeError, IndexError):
                # skip rows with invalid data
                continue

        # define chart dimensions for proper proportions
        line_chart_width = 800
        line_chart_height = 350
        pie_chart_width = 400
        pie_chart_height = 350

        # only create line chart if we have enough valid data rows
        if valid_data_rows >= 2:
            # 1. line chart for income and outcome over time
            line_chart = {
                "addChart": {  # api key
                    "chart": {  # api key
                        "spec": {  # api key
                            "title": f"income vs outcome for {worksheet_name.replace('transactions_', '')}",  # title, lc parts
                            "basicChart": {  # api key
                                "chartType": "LINE",  # api enum value
                                "legendPosition": "BOTTOM_LEGEND",  # api enum value
                                "axis": [  # api key
                                    {
                                        "position": "BOTTOM_AXIS",
                                        "title": "date",
                                    },  # api enum value, title lc
                                    {
                                        "position": "LEFT_AXIS",
                                        "title": "amount",
                                    },  # api enum value, title lc
                                ],
                                "domains": [
                                    {
                                        "domain": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": date_col,
                                                        "endColumnIndex": date_col + 1,
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],  # api keys
                                "series": [  # api key
                                    {
                                        "series": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": income_col,
                                                        "endColumnIndex": income_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",
                                        "color": {
                                            "red": 0.2,
                                            "green": 0.6,
                                            "blue": 0.2,
                                        },
                                    },  # api keys
                                    {
                                        "series": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": outcome_col,
                                                        "endColumnIndex": outcome_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",
                                        "color": {
                                            "red": 0.8,
                                            "green": 0.2,
                                            "blue": 0.2,
                                        },
                                    },  # api keys
                                ],
                                "headerCount": 1,  # api key
                            },
                        },
                        "position": {
                            "overlayPosition": {
                                "anchorCell": {
                                    "sheetId": sheet_id,
                                    "rowIndex": 1,
                                    "columnIndex": 0,
                                },
                                "widthPixels": line_chart_width,
                                "heightPixels": line_chart_height,
                            }
                        },  # api keys
                    }
                }
            }
            charts_request_body["requests"].append(line_chart)
        else:
            logger.info(
                f"skipping line chart for {worksheet_name}: insufficient numeric data (found {valid_data_rows} valid rows)"
            )

        # 2. aggregate transactions by tags for pie chart
        tags_aggregation_data = {}  # renamed
        valid_tag_rows = 0

        for row in data[1:]:  # skip header
            if not row[tags_col]:
                continue
            try:
                row_tags = row[tags_col].split(", ")
                outcome_val = float(row[outcome_col]) if row[outcome_col] else 0
                if outcome_val <= 0:
                    continue
                valid_tag_rows += 1
                tag_value = outcome_val / len(row_tags)
                for tag_item in row_tags:  # renamed
                    tags_aggregation_data[tag_item] = (
                        tags_aggregation_data.get(tag_item, 0) + tag_value
                    )
            except (ValueError, TypeError, IndexError):
                continue

        top_tags = sorted(
            tags_aggregation_data.items(), key=lambda x: x[1], reverse=True
        )[:5]
        pie_data_start_row = 1
        pie_data_col = 12

        clear_request = {
            "updateCells": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 10,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "fields": "userEnteredValue",
            }
        }  # api keys
        charts_request_body["requests"].append(clear_request)

        has_enough_data = valid_tag_rows >= 2 and len(top_tags) >= 1
        chart_data_header_req = {
            "updateCells": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 1,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "rows": [
                    {
                        "values": [
                            {"userEnteredValue": {"stringValue": "category"}},
                            {"userEnteredValue": {"stringValue": "amount"}},
                        ]
                    }
                ],
                "fields": "userEnteredValue",
            }
        }  # api keys, headers lc
        charts_request_body["requests"].append(chart_data_header_req)

        if has_enough_data:
            chart_data_values = [
                {
                    "values": [
                        {"userEnteredValue": {"stringValue": tag_item}},
                        {"userEnteredValue": {"numberValue": amount_val}},
                    ]
                }
                for tag_item, amount_val in top_tags
            ]  # data kept as is
            chart_data_values_req = {
                "updateCells": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 1 + len(top_tags),
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": chart_data_values,
                    "fields": "userEnteredValue",
                }
            }  # api keys
            charts_request_body["requests"].append(chart_data_values_req)
        else:
            logger.info(f"using placeholder data for pie chart in {worksheet_name}")
            placeholder_data = [
                {
                    "values": [
                        {"userEnteredValue": {"stringValue": "no data"}},
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
                {
                    "values": [
                        {"userEnteredValue": {"stringValue": "add transactions"}},
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
            ]  # placeholders lc
            placeholder_request = {
                "updateCells": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 3,
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": placeholder_data,
                    "fields": "userEnteredValue",
                }
            }  # api keys
            charts_request_body["requests"].append(placeholder_request)

        pie_chart_title = f"top expense categories for {worksheet_name.replace('transactions_', '')}"  # title, lc parts
        if not has_enough_data:
            pie_chart_title += " (not enough data)"  # title part, lc

        pie_chart = {
            "addChart": {  # api key
                "chart": {  # api key
                    "spec": {  # api key
                        "title": pie_chart_title,  # use var
                        "pieChart": {  # api key
                            "legendPosition": "RIGHT_LEGEND",  # api enum value
                            "domain": {
                                "sourceRange": {
                                    "sources": [
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col,
                                            "endColumnIndex": pie_data_col + 1,
                                        }
                                    ]
                                }
                            },  # api keys
                            "series": {
                                "sourceRange": {
                                    "sources": [
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col + 1,
                                            "endColumnIndex": pie_data_col + 2,
                                        }
                                    ]
                                }
                            },  # api keys
                            "pieHole": 0.3,  # api key
                        },
                    },
                    "position": {
                        "overlayPosition": {
                            "anchorCell": {
                                "sheetId": sheet_id,
                                "rowIndex": 1,
                                "columnIndex": 8,
                            },
                            "widthPixels": pie_chart_width,
                            "heightPixels": pie_chart_height,
                        }
                    },  # api keys
                }
            }
        }
        charts_request_body["requests"].append(pie_chart)

        if charts_request_body["requests"]:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=charts_request_body
            ).execute()
            logger.info(f"successfully added charts to {worksheet_name}")

    except Exception as e:
        logger.error(f"pizdec! failed to add charts to {worksheet_name}: {e}")
        import traceback

        logger.error(f"detailed error: {traceback.format_exc()}")


def update_monthly_sheets(monthly_data):
    """
    updates google sheets with monthly transaction data in separate worksheets.
    like filling different tanks for different uses.
    """
    if not monthly_data:
        logger.info("no monthly transaction data to upload.")
        return

    # get google sheets config
    google_creds_path = CONFIG["google_sheets"]["credentials_path"]
    spreadsheet_key = CONFIG["google_sheets"]["spreadsheet_key"]

    logger.info(
        f'connecting to google sheets api using service account: {google_creds_path or "default path"}'
    )
    try:
        if google_creds_path:
            gc = gspread.service_account(filename=google_creds_path)
        else:
            gc = gspread.service_account()
        logger.info("successfully authenticated with google sheets.")

        # open the spreadsheet (tank)
        try:
            sh = gc.open_by_key(spreadsheet_key)
            logger.info(
                f'opened spreadsheet by key: "{sh.title}"'
            )  # sh.title external, kept as is
        except SpreadsheetNotFound:
            logger.error(
                f'blyat! spreadsheet with key "{spreadsheet_key}" not found. check key and sharing permissions.'
            )
            return
        except Exception as e:
            logger.error(f"pizdec! could not open spreadsheet: {str(e)}")
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")
            return

        # set up the sheets api for chart creation
        from google.oauth2 import service_account

        SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]  # url, kept as is
        sheets_service = None  # initialize
        try:
            creds = service_account.Credentials.from_service_account_file(
                google_creds_path, scopes=SCOPES
            )
            sheets_service = build("sheets", "v4", credentials=creds)
            logger.info("initialized sheets api service for chart creation")
        except Exception as e:
            logger.error(f"pizdec! could not initialize sheets api service: {e}")
            # sheets_service remains none

        existing_worksheets = {
            ws.title: ws for ws in sh.worksheets()  # ws.title external, kept as is
        }

        for month, month_sheet_data in monthly_data.items():  # renamed
            worksheet_name = f"transactions_{month}"  # "transactions_" already lc

            try:
                if worksheet_name in existing_worksheets:
                    worksheet = existing_worksheets[worksheet_name]
                    logger.info(f'found existing worksheet: "{worksheet_name}"')
                    try:
                        worksheet.clear()
                        logger.info(
                            f"cleared existing data in worksheet {worksheet_name}."
                        )
                    except Exception as e:
                        logger.error(
                            f"blyat! could not clear worksheet {worksheet_name}: {str(e)}"
                        )
                        continue
                else:
                    rows_needed = len(month_sheet_data) + 40
                    cols_needed = len(month_sheet_data[0]) + 10
                    worksheet = sh.add_worksheet(
                        title=worksheet_name, rows=rows_needed, cols=cols_needed
                    )
                    logger.info(f'created new worksheet: "{worksheet_name}"')

                if sheets_service:  # only attempt padding if service is available
                    padding_request = {
                        "insertRange": {  # api key
                            "range": {  # api key
                                "sheetId": worksheet._properties[
                                    "sheetId"
                                ],  # api property
                                "startRowIndex": 0,
                                "endRowIndex": 20,
                                "startColumnIndex": 0,
                                "endColumnIndex": len(month_sheet_data[0]),
                            },
                            "shiftDimension": "ROWS",  # api enum value
                        }
                    }
                    sheets_service.spreadsheets().batchUpdate(
                        spreadsheetId=spreadsheet_key,
                        body={"requests": [padding_request]},
                    ).execute()
                    logger.info(f"added padding rows at the top of {worksheet_name}")

                logger.info(
                    f"writing {len(month_sheet_data)} rows (including header) to worksheet {worksheet_name}..."
                )
                worksheet.update(month_sheet_data, "A21")  # range string

                header_format = {"textFormat": {"bold": True}}  # api key
                worksheet.format(
                    f"A21:{chr(ord('A') + len(month_sheet_data[0]) - 1)}21",
                    header_format,
                )  # range string
                logger.info(
                    f"applied basic formatting to header row in {worksheet_name}."
                )

                if (
                    sheets_service
                ):  # only attempt column adjustments and charts if service is available
                    sheet_id = worksheet._properties["sheetId"]  # api property
                    column_widths = [
                        {"column": 0, "width": 90},
                        {"column": 1, "width": 70},
                        {"column": 2, "width": 120},
                        {"column": 3, "width": 70},
                        {"column": 4, "width": 120},
                        {"column": 5, "width": 70},
                        {"column": 6, "width": 250},
                        {"column": 7, "width": 100},
                        {"column": 8, "width": 100},
                        {"column": 9, "width": 280},
                        {"column": 10, "width": 130},
                        {"column": 11, "width": 130},
                    ]
                    column_requests = []
                    for col_def in column_widths:
                        column_requests.append(
                            {
                                "updateDimensionProperties": {  # api key
                                    "range": {
                                        "sheetId": sheet_id,
                                        "dimension": "COLUMNS",
                                        "startIndex": col_def["column"],
                                        "endIndex": col_def["column"] + 1,
                                    },  # api keys/enum
                                    "properties": {
                                        "pixelSize": col_def["width"]
                                    },  # api key
                                    "fields": "pixelSize",  # api key
                                }
                            }
                        )
                    if column_requests:
                        sheets_service.spreadsheets().batchUpdate(
                            spreadsheetId=spreadsheet_key,
                            body={"requests": column_requests},
                        ).execute()
                        logger.info(
                            f"adjusted column widths in {worksheet_name} based on content"
                        )

                    add_monthly_charts(
                        sheets_service,
                        spreadsheet_key,
                        worksheet_name,
                        sheet_id,
                        month_sheet_data,
                    )

            except Exception as e:
                logger.error(f"pizdec! error updating worksheet {worksheet_name}: {e}")
                import traceback

                logger.error(f"detailed error: {traceback.format_exc()}")

        logger.info("successfully updated monthly google sheets.")

    except Exception as e:
        logger.error(f"blyat! failed to update monthly google sheets: {e}")


# --- main execution - let the water flow! ---
if __name__ == "__main__":
    logger.info("starting zenmoney to google sheets export...")

    # get days to fetch from config
    days_to_fetch = CONFIG["settings"]["days_to_fetch"]
    transactions, accounts, tags = get_zenmoney_transactions(days_to_fetch)

    if transactions is not None:
        sheet_data = format_transactions_for_gsheet(transactions, accounts, tags)

        # update the main sheet as before
        update_google_sheet(sheet_data)

        # organize transactions by month and update monthly sheets
        monthly_data = organize_transactions_by_month(sheet_data)
        update_monthly_sheets(monthly_data)

    else:
        logger.warning("failed to fetch transactions, cannot update google sheet.")

    logger.info("export process finished.")
