from __future__ import annotations

import gspread
import googleapiclient.discovery  # moved to top level import
from google.oauth2 import service_account
from loguru import logger

from ..visualization.charts import add_monthly_charts


class GoogleSheetsClient:
    """wrapper around gspread service for common operations."""

    def __init__(self, credentials_path: str):
        self.credentials_path = credentials_path
        self.gc = gspread.service_account(filename=credentials_path)
        logger.info("authenticated to google sheets api")

    def open_by_key(self, key: str):
        logger.info(f"opening spreadsheet with key {key}")
        return self.gc.open_by_key(key)

    # --- high-level operations ---

    def upload_general_sheet(self, data, spreadsheet_key: str, worksheet_name: str):
        """upload formatted transaction data to general worksheet and call chart helper stub."""
        # import moved to top level
        # from google.oauth2 import service_account already at top level

        if not data or len(data) <= 1:
            logger.info("no new transaction data to upload – skipping")
            return

        sh = self.open_by_key(spreadsheet_key)

        # get or create worksheet
        try:
            worksheet = sh.worksheet(worksheet_name)
            worksheet.clear()
            logger.info(f"cleared existing data in worksheet {worksheet_name}")
        except gspread.exceptions.WorksheetNotFound:
            rows_needed = len(data) + 10
            cols_needed = len(data[0]) + 2
            worksheet = sh.add_worksheet(
                title=worksheet_name, rows=rows_needed, cols=cols_needed
            )
            logger.info(f"created new worksheet {worksheet_name}")

        # write data starting a1
        worksheet.update(data, "a1")
        header_format = {"textFormat": {"bold": True, "fontFamily": "Inter"}}
        worksheet.format(f"A1:{chr(ord('A') + len(data[0]) - 1)}1", header_format)

        # apply visual formatting (borders, column widths, hide transaction_id)
        try:
            creds = service_account.Credentials.from_service_account_file(
                self.credentials_path,
                scopes=["https://www.googleapis.com/auth/spreadsheets"],
            )
            sheets_service = googleapiclient.discovery.build(
                "sheets", "v4", credentials=creds
            )

            sheet_id = worksheet._properties["sheetId"]
            self._apply_transactions_table_formatting(
                sheets_service,
                spreadsheet_key,
                sheet_id,
                num_cols=len(data[0]),
                num_rows=len(data),
                start_row_index=0,
            )
        except Exception as e:
            logger.error(f"failed to apply table formatting: {e}")

        # optional chart creation skipped in refactor stage 2
        logger.debug("chart creation skipped in refactor stage 2")

    def _sort_months_chronologically(self, monthly_data: dict) -> list:
        """sort month keys chronologically from earliest to latest."""

        def month_to_sort_key(month_name: str) -> tuple:
            """convert Russian month name like 'МАЙ-2025' to sortable tuple (year, month_num)."""
            if month_name == "unknown":
                return (9999, 99)  # put unknown at the end

            try:
                month_part, year_part = month_name.split("-")
                year = int(year_part)

                # Russian month names to numbers
                russian_months = {
                    "ЯНВАРЬ": 1,
                    "ФЕВРАЛЬ": 2,
                    "МАРТ": 3,
                    "АПРЕЛЬ": 4,
                    "МАЙ": 5,
                    "ИЮНЬ": 6,
                    "ИЮЛЬ": 7,
                    "АВГУСТ": 8,
                    "СЕНТЯБРЬ": 9,
                    "ОКТЯБРЬ": 10,
                    "НОЯБРЬ": 11,
                    "ДЕКАБРЬ": 12,
                }

                month_num = russian_months.get(month_part, 99)
                return (year, month_num)
            except (ValueError, IndexError):
                return (9999, 99)  # fallback for malformed names

        return sorted(monthly_data.keys(), key=month_to_sort_key)

    def upload_monthly_sheets(
        self,
        monthly_data: dict,
        spreadsheet_key: str,
        create_charts: bool = True,
        loans_data: dict | None = None,
    ):
        """iterate over monthly grouped data and upload each to its own sheet."""
        if not monthly_data:
            logger.info("no monthly data to upload")
            return

        sh = self.open_by_key(spreadsheet_key)
        existing = {ws.title: ws for ws in sh.worksheets()}

        # initialize google sheets api service for chart creation when needed
        sheets_service = None
        if create_charts:
            try:
                creds = service_account.Credentials.from_service_account_file(
                    self.credentials_path,
                    scopes=["https://www.googleapis.com/auth/spreadsheets"],
                )
                sheets_service = googleapiclient.discovery.build(
                    "sheets", "v4", credentials=creds
                )
                logger.info("initialized sheets api service for chart creation")
            except Exception as e:
                logger.error(f"failed to initialize sheets api service: {e}")
                sheets_service = None

        # sort months chronologically (earliest to latest)
        sorted_months = self._sort_months_chronologically(monthly_data)
        logger.info(f"processing months in chronological order: {sorted_months}")

        for month in sorted_months:
            rows = monthly_data[month]
            # month is now in Russian format like МАЙ-2025 or АПРЕЛЬ-2025
            ws_title = month
            # always ensure a pristine sheet by recreating it – this removes all residual formatting
            if ws_title in existing:
                try:
                    sh.del_worksheet(existing[ws_title])
                    logger.info(
                        f"deleted existing worksheet {ws_title} to reset formatting"
                    )
                except Exception as e:
                    logger.error(f"failed to delete worksheet {ws_title}: {e}")

            # (re)create worksheet with sufficient rows/cols buffer
            try:
                ws = sh.add_worksheet(
                    title=ws_title,
                    rows=len(rows) + 400,
                    cols=len(rows[0]) + 10,
                )
                logger.info(f"created worksheet {ws_title}")
            except Exception as e:
                logger.error(f"failed to create worksheet {ws_title}: {e}")
                # fallback: attempt to reference existing if creation fails
                ws = existing.get(ws_title) if ws_title in existing else None
                if ws is None:
                    raise

            # ensure worksheet has enough rows for incoming data (+40 buffer)
            required_rows = len(rows) + 40
            if ws.row_count < required_rows:
                ws.add_rows(required_rows - ws.row_count)

            # write data starting a21 (allowing room for charts)
            ws.update(rows, "A21")
            header_format = {"textFormat": {"bold": True, "fontFamily": "Inter"}}
            ws.format(f"A21:{chr(ord('A') + len(rows[0]) - 1)}21", header_format)
            logger.info(f"uploaded {len(rows) - 1} rows to {ws_title}")

            # add charts if requested and service available
            if create_charts and sheets_service:
                try:
                    # get sheet id for this worksheet
                    sheet_id = ws._properties["sheetId"]

                    # add charts to the monthly worksheet
                    add_monthly_charts(
                        sheets_service, spreadsheet_key, ws_title, sheet_id, rows
                    )

                    # also apply consistent table formatting (hide transaction id, borders, widths)
                    try:
                        self._apply_transactions_table_formatting(
                            sheets_service,
                            spreadsheet_key,
                            sheet_id,
                            num_cols=len(rows[0]),
                            num_rows=len(rows),
                            start_row_index=20,  # data starts at row index 20 (row 21)
                        )
                    except Exception as e:
                        logger.error(f"failed table formatting for {ws_title}: {e}")

                    # always write loan tables (with placeholder rows when no data)
                    if loans_data is None:
                        loans_data = {}

                    loans_for_month = loans_data.get(month, {"given": [], "taken": []})

                    try:
                        self._write_loan_tables(
                            ws,
                            sheets_service if sheets_service else None,
                            spreadsheet_key,
                            sheet_id,
                            loans_for_month.get("given", []),
                            loans_for_month.get("taken", []),
                        )
                    except Exception as e:
                        logger.error(f"failed to write loan tables for {ws_title}: {e}")
                except Exception as e:
                    logger.error(f"failed to add charts to {ws_title}: {e}")

        logger.info("monthly worksheets cleanup complete")

    # --- cleaning helpers ---

    def clear_general_sheet(self, spreadsheet_key: str, worksheet_name: str):
        """delete all rows in the general worksheet but keep the sheet."""
        sh = self.open_by_key(spreadsheet_key)
        try:
            ws = sh.worksheet(worksheet_name)
            ws.clear()
            logger.info(f"cleared data in worksheet {worksheet_name}")
        except gspread.exceptions.WorksheetNotFound:
            logger.warning(f"worksheet {worksheet_name} not found – nothing to clean")

    def clear_monthly_sheets(self, spreadsheet_key: str):
        """remove all worksheets with Russian month names or old 'transactions_' prefix."""
        sh = self.open_by_key(spreadsheet_key)

        # Russian month names for pattern matching
        russian_months = [
            "ЯНВАРЬ",
            "ФЕВРАЛЬ",
            "МАРТ",
            "АПРЕЛЬ",
            "МАЙ",
            "ИЮНЬ",
            "ИЮЛЬ",
            "АВГУСТ",
            "СЕНТЯБРЬ",
            "ОКТЯБРЬ",
            "НОЯБРЬ",
            "ДЕКАБРЬ",
        ]

        for ws in sh.worksheets():
            # Check for old format (transactions_YYYY-MM) or new Russian format (МЕСЯЦ-YYYY)
            should_delete = (
                ws.title.startswith("transactions_")
                or any(ws.title.startswith(f"{month}-") for month in russian_months)
                or ws.title == "unknown"
            )

            if should_delete:
                logger.info(f"deleting worksheet {ws.title}")
                sh.del_worksheet(ws)
        logger.info("monthly worksheets cleanup complete")

    # additional helper methods will be implemented as needed

    # --- private helpers ---

    def _apply_transactions_table_formatting(
        self,
        service,
        spreadsheet_key: str,
        sheet_id: int,
        num_cols: int,
        num_rows: int,
        start_row_index: int = 0,
    ):
        """apply borders, hide transaction id column, and set column widths.

        this matches the style used by the mini category chart tables for consistency.
        """

        # medium black border for clear grid appearance
        medium_black = {
            "style": "SOLID",
            "width": 2,
            "color": {"red": 0.0, "green": 0.0, "blue": 0.0},
        }

        end_row_index = start_row_index + num_rows

        # fetch sheet total row count to extend borders till bottom
        try:
            metadata = (
                service.spreadsheets()
                .get(
                    spreadsheetId=spreadsheet_key,
                    ranges=[],
                    includeGridData=False,
                    fields="sheets(properties(sheetId,gridProperties(rowCount)))",
                )
                .execute()
            )

            total_rows = None
            for _s in metadata.get("sheets", []):
                props = _s.get("properties", {})
                if props.get("sheetId") == sheet_id:
                    grid = props.get("gridProperties", {})
                    total_rows = grid.get("rowCount")
                    break

            if total_rows is None:
                total_rows = end_row_index  # fallback
        except Exception:
            total_rows = end_row_index  # fallback when metadata fetch fails

        requests = [
            {
                "updateBorders": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": start_row_index,
                        "endRowIndex": end_row_index,
                        "startColumnIndex": 0,
                        "endColumnIndex": num_cols,
                    },
                    "top": medium_black,
                    "bottom": medium_black,
                    "left": medium_black,
                    "right": medium_black,
                    "innerHorizontal": medium_black,
                    "innerVertical": medium_black,
                }
            },
            # hide transaction_id column (index 10 → k)
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 10,
                        "endIndex": 11,
                    },
                    "properties": {"hiddenByUser": True},
                    "fields": "hiddenByUser",
                }
            },
            # apply inter font to entire table range
            {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": start_row_index,
                        "endRowIndex": end_row_index,
                        "startColumnIndex": 0,
                        "endColumnIndex": num_cols,
                    },
                    "cell": {
                        "userEnteredFormat": {"textFormat": {"fontFamily": "Inter"}}
                    },
                    "fields": "userEnteredFormat.textFormat.fontFamily",
                }
            },
            # extend borders from the end of data down to bottom of sheet to maintain continuous grid
            {
                "updateBorders": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": end_row_index,
                        "endRowIndex": total_rows,
                        "startColumnIndex": 0,
                        "endColumnIndex": num_cols,
                    },
                    "left": medium_black,
                    "right": medium_black,
                    "innerVertical": medium_black,
                    "innerHorizontal": medium_black,
                }
            },
        ]

        # column widths for first 13 columns (a–m)
        col_pixel_sizes = [
            90,  # date
            70,  # type
            120,  # income_account
            90,  # income
            120,  # outcome_account
            90,  # outcome
            70,  # currency
            220,  # payee
            180,  # comment
            120,  # tags
            280,  # transaction_id (hidden, but width kept)
            130,  # created_at
            130,  # changed_at
        ]

        for idx, pixel in enumerate(col_pixel_sizes):
            requests.append(
                {
                    "updateDimensionProperties": {
                        "range": {
                            "sheetId": sheet_id,
                            "dimension": "COLUMNS",
                            "startIndex": idx,
                            "endIndex": idx + 1,
                        },
                        "properties": {"pixelSize": pixel},
                        "fields": "pixelSize",
                    }
                }
            )

        # execute all formatting requests in a single batch
        try:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={"requests": requests},
            ).execute()
            logger.info(
                "applied transaction table formatting (borders, hide column, widths)"
            )
        except Exception as e:
            logger.error(f"failed to apply transaction table formatting: {e}")

    def _write_loan_tables(
        self,
        ws,
        service,
        spreadsheet_key: str,
        sheet_id: int,
        loans_given: list,
        loans_taken: list,
    ):
        """write loan tables starting at column r2 and apply basic formatting."""
        # ensure at least header rows exist
        if not loans_given:
            loans_given = [
                [
                    "контрагент",
                    "дата",
                    "сумма",
                    "статус",
                    "срок погашения",
                    "примечание",
                ],
                ["нет данных", "", "", "", "", ""],
            ]
        elif len(loans_given) == 1:
            loans_given.append(["нет данных", "", "", "", "", ""])

        if not loans_taken:
            loans_taken = [
                [
                    "контрагент",
                    "дата",
                    "сумма",
                    "статус",
                    "срок погашения",
                    "примечание",
                ],
                ["нет данных", "", "", "", "", ""],
            ]
        elif len(loans_taken) == 1:
            loans_taken.append(["нет данных", "", "", "", "", ""])

        import string

        def col_letter(idx: int) -> str:
            result = ""
            while idx >= 0:
                result = chr(ord("A") + (idx % 26)) + result
                idx = idx // 26 - 1
            return result

        start_col_index = 17  # column R (0-indexed)
        start_col_letter = col_letter(start_col_index)

        # loans given table position (shifted down by one row)
        ws.update(loans_given, f"{start_col_letter}3")

        # insert title for loans given (row 2)
        if service:
            title_requests = []
            title_requests.append(
                {
                    "mergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 1,
                            "endRowIndex": 2,
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "mergeType": "MERGE_ALL",
                    }
                }
            )
            title_requests.append(
                {
                    "updateCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 1,
                            "endRowIndex": 2,
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "rows": [
                            {
                                "values": [
                                    {
                                        "userEnteredValue": {
                                            "stringValue": "выданные займы"
                                        },
                                        "userEnteredFormat": {
                                            "textFormat": {
                                                "bold": True,
                                                "fontSize": 12,
                                                "fontFamily": "Inter",
                                            }
                                        },
                                    }
                                ]
                            }
                        ],
                        "fields": "userEnteredValue,userEnteredFormat.textFormat",
                    }
                }
            )
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body={"requests": title_requests}
            ).execute()

        # loans taken table starts after loans_given rows + one blank row
        offset = len(loans_given) + 2  # previously +1, now leave extra blank row
        ws.update(loans_taken, f"{start_col_letter}{3 + offset}")

        # title for loans taken
        if service:
            title2_row = offset + 1  # row index before taken header
            title2_requests = [
                {
                    "mergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": title2_row,
                            "endRowIndex": title2_row + 1,
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "mergeType": "MERGE_ALL",
                    }
                },
                {
                    "updateCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": title2_row,
                            "endRowIndex": title2_row + 1,
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "rows": [
                            {
                                "values": [
                                    {
                                        "userEnteredValue": {
                                            "stringValue": "полученные займы"
                                        },
                                        "userEnteredFormat": {
                                            "textFormat": {
                                                "bold": True,
                                                "fontSize": 12,
                                                "fontFamily": "Inter",
                                            }
                                        },
                                    }
                                ]
                            }
                        ],
                        "fields": "userEnteredValue,userEnteredFormat.textFormat",
                    }
                },
            ]
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body={"requests": title2_requests}
            ).execute()

        # header formatting bold for both tables
        amount_col_letter = col_letter(
            start_col_index + 2
        )  # amount column (counterparty=0,date=1,amount=2)

        header_format = {"textFormat": {"bold": True, "fontFamily": "Inter"}}
        ws.format(
            f"{start_col_letter}3:{col_letter(start_col_index + 5)}3",
            header_format,
        )
        ws.format(
            f"{start_col_letter}{3+offset}:{col_letter(start_col_index+5)}{3+offset}",
            header_format,
        )

        # apply background color for loans given header (light blue)
        if service:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={
                    "requests": [
                        {
                            "repeatCell": {
                                "range": {
                                    "sheetId": sheet_id,
                                    "startRowIndex": 2,
                                    "endRowIndex": 3,
                                    "startColumnIndex": start_col_index,
                                    "endColumnIndex": start_col_index + 6,
                                },
                                "cell": {
                                    "userEnteredFormat": {
                                        "backgroundColor": {
                                            "red": 0.83,
                                            "green": 0.91,
                                            "blue": 1,
                                        },
                                        "textFormat": {
                                            "bold": True,
                                            "fontFamily": "Inter",
                                        },
                                    }
                                },
                                "fields": "userEnteredFormat(backgroundColor,textFormat)",
                            }
                        }
                    ]
                },
            ).execute()

        # background color for loans taken header (light pink)
        if service:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={
                    "requests": [
                        {
                            "repeatCell": {
                                "range": {
                                    "sheetId": sheet_id,
                                    "startRowIndex": 2 + offset,
                                    "endRowIndex": 3 + offset,
                                    "startColumnIndex": start_col_index,
                                    "endColumnIndex": start_col_index + 6,
                                },
                                "cell": {
                                    "userEnteredFormat": {
                                        "backgroundColor": {
                                            "red": 1,
                                            "green": 0.87,
                                            "blue": 0.91,
                                        },
                                        "textFormat": {
                                            "bold": True,
                                            "fontFamily": "Inter",
                                        },
                                    }
                                },
                                "fields": "userEnteredFormat(backgroundColor,textFormat)",
                            }
                        }
                    ]
                },
            ).execute()

        # adjust column widths for loan tables (counterparty 200px etc.)
        col_widths = [200, 90, 100, 90, 120, 220]  # for columns r–w
        if service:
            width_reqs = []
            for idx, px in enumerate(col_widths):
                width_reqs.append(
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "COLUMNS",
                                "startIndex": start_col_index + idx,
                                "endIndex": start_col_index + idx + 1,
                            },
                            "properties": {"pixelSize": px},
                            "fields": "pixelSize",
                        }
                    }
                )

            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body={"requests": width_reqs}
            ).execute()

        if service:
            # apply number format to amount columns for both tables
            requests = []
            for header_row, data_len in [
                (2, len(loans_given) - 1),
                (2 + offset, len(loans_taken) - 1),
            ]:
                if data_len <= 0:
                    continue
                requests.append(
                    {
                        "repeatCell": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": header_row + 1,  # skip header
                                "endRowIndex": header_row + 1 + data_len,
                                "startColumnIndex": start_col_index + 2,
                                "endColumnIndex": start_col_index + 3,
                            },
                            "cell": {
                                "userEnteredFormat": {
                                    "numberFormat": {
                                        "type": "NUMBER",
                                        "pattern": '#,##0.00 "₽"',
                                    }
                                }
                            },
                            "fields": "userEnteredFormat.numberFormat",
                        }
                    }
                )

            if requests:
                service.spreadsheets().batchUpdate(
                    spreadsheetId=spreadsheet_key, body={"requests": requests}
                ).execute()

            # apply borders to both tables using sheets api if available
            medium_black = {
                "style": "SOLID",
                "width": 2,
                "color": {"red": 0.0, "green": 0.0, "blue": 0.0},
            }

            borders_req = []
            # loans given range
            borders_req.append(
                {
                    "updateBorders": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 2,  # row3 index2
                            "endRowIndex": 2 + len(loans_given),
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "top": medium_black,
                        "bottom": medium_black,
                        "left": medium_black,
                        "right": medium_black,
                        "innerHorizontal": medium_black,
                        "innerVertical": medium_black,
                    }
                }
            )

            # loans taken borders
            start_row_taken = 2 + offset
            borders_req.append(
                {
                    "updateBorders": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": start_row_taken,
                            "endRowIndex": start_row_taken + len(loans_taken),
                            "startColumnIndex": start_col_index,
                            "endColumnIndex": start_col_index + 6,
                        },
                        "top": medium_black,
                        "bottom": medium_black,
                        "left": medium_black,
                        "right": medium_black,
                        "innerHorizontal": medium_black,
                        "innerVertical": medium_black,
                    }
                }
            )

            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body={"requests": borders_req}
            ).execute()
