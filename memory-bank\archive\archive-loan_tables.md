# archive – loan tracking tables (2025-06-15)

## summary

implemented automatic loan tracking tables ("выданные займы" & "полученные займы") across all monthly worksheets.

## context

users needed clear visibility of loan-related transactions. existing sheets lacked structured representation.

## implementation highlights

-   added loan detection via account type (`loan`, `debt`) and keyword matching.
-   processing.loans module formats data; organize_loans_by_month groups by russian month names.
-   sheets api writes tables at column R, with titles, colored headers, borders, column widths, placeholder row when no data.
-   demo_print_loans.py script prints loans for debugging.

## files changed/added

-   zenmoney_exporter/processing/loans.py (new)
-   zenmoney_exporter/cli.py (sync integration)
-   zenmoney_exporter/sheets/api.py (table writing, styling)
-   demo_print_loans.py (demo script)

## verification

-   manual sync shows tables on all sheets with proper formatting.
-   pytest suite still passes (11 tests).
-   reflection document: memory-bank/reflection/reflection-loan_tables.md

## status

completed and archived on 2025-06-15.
