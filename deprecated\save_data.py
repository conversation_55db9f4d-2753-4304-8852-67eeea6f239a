import json
import os
import uuid
import tempfile
from datetime import datetime
from loguru import logger
import sys

# import zenmoney api components
from zenmoney import Request, Diff
from zenmoney.exception import ZenMoneyException

# setup logging - colorful but lowercase
logger.remove()
logger.add(
    sink=sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> - <level>{level}</level> - <cyan>{message}</cyan>",
    colorize=True,
    level="INFO",
)


def load_config():
    """
    load configuration from yaml file.
    """
    import yaml

    # go up one directory from deprecated folder to project root
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    config_path = os.path.join(project_root, "credentials", "config.yaml")

    # check if config file exists
    if not os.path.exists(config_path):
        logger.error("config file not found at: {}".format(config_path))
        return None

    # load config from yaml file
    try:
        with open(config_path, "r", encoding="utf-8") as file:
            return yaml.safe_load(file)
    except yaml.YAMLError as e:
        logger.error(f"error parsing config.yaml: {e}")
        return None
    except Exception as e:
        logger.error(f"unknown error loading config: {e}")
        return None


def fetch_full_zenmoney_data():
    """
    fetches the complete database from zenmoney api
    """
    config = load_config()
    if not config:
        logger.error("cannot continue without configuration!")
        return None

    logger.info("connecting to zenmoney api...")
    try:
        api = Request(config["zenmoney"]["api_key"])
        logger.info("successfully authenticated with zenmoney.")
    except Exception as e:
        logger.error(f"failed to authenticate with zenmoney: {e}")
        return None

    logger.info("requesting full database from zenmoney (this might take a while)...")

    try:
        # use serverTimestamp=1 to get the entire database
        diff_request = Diff(serverTimestamp=1)
        diff_response = api.diff(diff_request)
        logger.info(
            f"received full database from server. new server timestamp: {diff_response.serverTimestamp}"
        )

        # log some stats about what we received
        transactions = getattr(diff_response, "transaction", [])
        accounts = getattr(diff_response, "account", [])
        tags = getattr(diff_response, "tag", [])

        logger.info(
            f"fetched {len(transactions)} transactions, {len(accounts)} accounts, and {len(tags)} tags"
        )

        # return the raw response object
        return diff_response

    except ZenMoneyException as e:
        logger.error(f"zenmoney api error: {e}")
        return None
    except Exception as e:
        logger.error(f"unknown error fetching zenmoney data: {e}")
        return None


def save_to_temp_json(data):
    """
    saves data to a random temp json file
    """
    if not data:
        logger.error("no data to save")
        return None

    # create a random filename
    random_id = str(uuid.uuid4())[:8]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"zenmoney_full_db_{timestamp}_{random_id}.json"

    # use temp directory
    temp_dir = tempfile.gettempdir()
    filepath = os.path.join(temp_dir, filename)

    try:
        # convert object to dictionary if possible
        if hasattr(data, "__dict__"):
            # handle nested objects with custom serialization
            def serialize_obj(obj):
                if hasattr(obj, "__dict__"):
                    return obj.__dict__
                return str(obj)

            # serialize the data with a custom converter
            serialized_data = json.dumps(data, default=serialize_obj, indent=2)

            with open(filepath, "w", encoding="utf-8") as f:
                f.write(serialized_data)

            logger.info(f"full database saved to temporary file: {filepath}")
            return filepath

    except TypeError as e:
        # if direct serialization fails, create a simplified representation
        logger.warning(f"couldn't directly serialize object: {e}")

        try:
            # extract key attributes in a dict
            simplified_data = {
                "serverTimestamp": getattr(data, "serverTimestamp", None),
                "timestamp": datetime.now().isoformat(),
                "transaction_count": len(getattr(data, "transaction", [])),
                "account_count": len(getattr(data, "account", [])),
                "tag_count": len(getattr(data, "tag", [])),
                "has_data": bool(data),
            }

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(simplified_data, f, indent=2)

            logger.info(
                f"simplified database summary saved to temporary file: {filepath}"
            )
            return filepath

        except Exception as e2:
            logger.error(f"failed to save simplified data: {e2}")
            return None

    except Exception as e:
        logger.error(f"failed to save data: {e}")
        return None


def save_to_custom_location(data, output_dir=None):
    """
    saves data to a specified location or current directory
    """
    if not data:
        logger.error("no data to save")
        return None

    # create a filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"zenmoney_full_db_{timestamp}.json"

    # use specified directory or current directory
    if output_dir and os.path.isdir(output_dir):
        filepath = os.path.join(output_dir, filename)
    else:
        filepath = os.path.join(os.getcwd(), filename)

    try:
        # convert object to dictionary if possible
        if hasattr(data, "__dict__"):
            # handle nested objects with custom serialization
            def serialize_obj(obj):
                if hasattr(obj, "__dict__"):
                    return obj.__dict__
                return str(obj)

            # serialize the data with a custom converter
            serialized_data = json.dumps(data, default=serialize_obj, indent=2)

            with open(filepath, "w", encoding="utf-8") as f:
                f.write(serialized_data)

            logger.info(f"full database saved to file: {filepath}")
            return filepath

    except TypeError as e:
        logger.error(f"couldn't serialize object: {e}")
        return None

    except Exception as e:
        logger.error(f"failed to save data: {e}")
        return None


if __name__ == "__main__":
    logger.info("starting full database download...")

    # determine output location (optional)
    output_dir = None
    if len(sys.argv) > 1:
        if os.path.isdir(sys.argv[1]):
            output_dir = sys.argv[1]
            logger.info(f"will save to specified directory: {output_dir}")
        else:
            logger.warning(
                f"specified path '{sys.argv[1]}' is not a valid directory, using default"
            )

    # fetch and save data
    raw_data = fetch_full_zenmoney_data()
    if raw_data:
        # save to temp and custom location
        temp_file = save_to_temp_json(raw_data)
        custom_file = save_to_custom_location(raw_data, output_dir)

        if temp_file or custom_file:
            logger.info("process completed successfully")
            if temp_file:
                logger.info(f"temporary file saved to: {temp_file}")
            if custom_file:
                logger.info(f"permanent file saved to: {custom_file}")
        else:
            logger.error("failed to save data to file")
    else:
        logger.error("failed to fetch data from zenmoney")
