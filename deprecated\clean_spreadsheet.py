import gspread
from loguru import logger
from googleapiclient.discovery import build
from google.oauth2 import service_account
import sys
import os
import yaml
import time

# setup logging with loguru
logger.remove()
logger.add(
    sink=sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> - <level>{level}</level> - <cyan>{message}</cyan>",
    colorize=True,
    level="INFO",
)


def load_config():
    """
    load configuration from yaml file.
    """
    config_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)) + "/credentials/config.yaml"
    )

    # check if config file exists
    if not os.path.exists(config_path):
        logger.error("config file not found at: {}".format(config_path))
        logger.error("please create config.yaml file from example or run setup")
        return None

    # load config from yaml file
    try:
        with open(config_path, "r") as file:
            return yaml.safe_load(file)
    except yaml.YAMLError as e:
        logger.error(f"error parsing config.yaml: {e}")
        return None
    except Exception as e:
        logger.error(f"unknown error loading config: {e}")
        return None


def clean_spreadsheet():
    """
    completely clean the google spreadsheet by removing all worksheets
    and recreating only the main worksheet.
    """
    # load configuration
    config = load_config()
    if not config:
        logger.error("cannot continue without proper configuration! exiting...")
        sys.exit(1)

    # get google sheets config
    google_creds_path = config["google_sheets"]["credentials_path"]
    spreadsheet_key = config["google_sheets"]["spreadsheet_key"]
    main_worksheet_name = config["google_sheets"]["worksheet_name"]

    logger.info(
        f'connecting to google sheets api using service account: {google_creds_path or "default path"}...'
    )
    try:
        if google_creds_path:
            gc = gspread.service_account(filename=google_creds_path)
        else:
            # try default paths
            gc = gspread.service_account()
        logger.info("successfully authenticated with google sheets.")

        # open the spreadsheet
        try:
            sh = gc.open_by_key(spreadsheet_key)
            logger.info(f'opened spreadsheet by key: "{sh.title}"')
        except Exception as e:
            logger.error(f"could not open spreadsheet: {str(e)}")
            import traceback

            logger.error(f"detailed error: {traceback.format_exc()}")
            return

        # setup sheets api for removing charts
        SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]
        try:
            creds = service_account.Credentials.from_service_account_file(
                google_creds_path, scopes=SCOPES
            )
            sheets_service = build("sheets", "v4", credentials=creds)
            logger.info("initialized sheets api service for chart removal")
        except Exception as e:
            logger.error(f"could not initialize sheets api service: {e}")
            sheets_service = None

        # get all worksheet titles
        worksheets = sh.worksheets()
        worksheet_titles = [ws.title for ws in worksheets]
        logger.info(f"found {len(worksheets)} worksheets: {worksheet_titles}")

        # delete all charts from each worksheet
        if sheets_service:
            for ws in worksheets:
                sheet_id = ws._properties["sheetId"]

                # get the sheet metadata to find all charts
                sheet_metadata = (
                    sheets_service.spreadsheets()
                    .get(
                        spreadsheetId=spreadsheet_key, ranges=[], includeGridData=False
                    )
                    .execute()
                )

                # find all charts on this sheet
                charts_to_delete = []
                if "sheets" in sheet_metadata:
                    for sheet in sheet_metadata["sheets"]:
                        if sheet.get("properties", {}).get("sheetId") == sheet_id:
                            if "charts" in sheet:
                                for i, chart in enumerate(sheet["charts"]):
                                    chart_id = chart.get("chartId")
                                    if chart_id:
                                        charts_to_delete.append(chart_id)

                # delete all charts if any were found
                if charts_to_delete:
                    delete_requests = []
                    for chart_id in charts_to_delete:
                        delete_requests.append(
                            {"deleteEmbeddedObject": {"objectId": chart_id}}
                        )

                    if delete_requests:
                        sheets_service.spreadsheets().batchUpdate(
                            spreadsheetId=spreadsheet_key,
                            body={"requests": delete_requests},
                        ).execute()
                        logger.info(
                            f"deleted {len(delete_requests)} charts from worksheet '{ws.title}'"
                        )

                # clear all data
                try:
                    ws.clear()
                    logger.info(f"cleared all data from worksheet '{ws.title}'")
                except Exception as e:
                    logger.error(f"could not clear worksheet '{ws.title}': {e}")

        # delete all worksheets except the main one
        # if main worksheet doesn't exist, we'll create it later
        for ws in worksheets:
            if ws.title != main_worksheet_name:
                try:
                    sh.del_worksheet(ws)
                    logger.info(f"deleted worksheet '{ws.title}'")
                    # add a small delay to avoid quota issues
                    time.sleep(0.5)
                except Exception as e:
                    logger.error(f"could not delete worksheet '{ws.title}': {e}")

        # check if main worksheet exists, if not create it
        try:
            main_ws = sh.worksheet(main_worksheet_name)
            logger.info(f"main worksheet '{main_worksheet_name}' preserved")
        except Exception:
            logger.info(
                f"main worksheet '{main_worksheet_name}' not found, creating it"
            )
            main_ws = sh.add_worksheet(title=main_worksheet_name, rows=100, cols=20)
            logger.info(f"created new main worksheet '{main_worksheet_name}'")

        logger.info("spreadsheet has been completely cleaned!")
        logger.info("ready for fresh data import")

    except Exception as e:
        logger.error(f"failed to clean spreadsheet: {e}")
        import traceback

        logger.error(f"detailed error: {traceback.format_exc()}")


if __name__ == "__main__":
    logger.info("starting spreadsheet cleaning process...")
    clean_spreadsheet()
    logger.info("cleaning process completed.")
