# technical context

## language & runtime

-   **python**: primary language used for all components
-   **version**: compatible with python 3.8+ (inferred from dependencies)
-   **virtual environment**: project includes a `.venv` directory, suggesting virtualenv or venv usage
-   **dependency management**: uses requirements.txt for dependencies; uv.lock suggests uv package manager

## core libraries

-   **zenmoney**: python client for zenmoney api (v0.9+)
-   **gspread**: python api wrapper for google sheets (v6.2.0+)
-   **loguru**: enhanced logging library (v0.7.0+)
-   **pyyaml**: yaml processing for configuration (v6.0+)
-   **google-api-python-client**: google api client (v2.100.0+)

## data flow architecture

```
zenmoney api → python application → google sheets api
    (source)      (processing)        (destination)
```

## data processing

1. **fetch**: retrieve transaction data from zenmoney api with historical tags
2. **transform**: format transactions for spreadsheet compatibility
3. **organize**: categorize transactions by month
4. **visualize**: create charts for income/outcome and expense categories
5. **upload**: send formatted data to google sheets

## authentication mechanism

-   **zenmoney**: api key authentication stored in config.yaml
-   **google sheets**: service account authentication using json key file

## file structure

-   **application logic**: main.py (primary script)
-   **utilities**: clean_spreadsheet.py, save_data.py
-   **configuration**: credentials/config.yaml
-   **authentication**: credentials/account.json
-   **dependency management**: requirements.txt, uv.lock, pyproject.toml

## logging

-   uses loguru for enhanced logging with colorized output
-   configurable log level through config.yaml

## error handling

-   comprehensive try/except blocks for api interactions
-   specific error handling for zenmoney api and google sheets api
-   graceful degradation when services are unavailable

## special considerations

-   handles currency conversions and transfers between accounts
-   manages transaction categorization through tags
-   creates visualizations for financial analysis
