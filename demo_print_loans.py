import sys
from pathlib import Path
from loguru import logger
from zenmoney_exporter.zen.api import ZenMoneyClient
from zenmoney_exporter.processing.loans import format_loans_for_gsheet
import yaml

CONFIG_PATH = Path("credentials/config.yaml")


def load_api_key() -> str:
    if not CONFIG_PATH.exists():
        logger.error("config.yaml not found at {}", CONFIG_PATH)
        sys.exit(1)
    with CONFIG_PATH.open("r", encoding="utf-8") as f:
        cfg = yaml.safe_load(f)
    return cfg["zenmoney"]["api_key"]


def main(days: int = 365):
    api_key = load_api_key()
    client = ZenMoneyClient(api_key)

    transactions, accounts, _tags = client.get_transactions(days)
    if transactions is None:
        logger.error("failed to fetch transactions")
        sys.exit(1)

    loans_given_rows, loans_taken_rows = format_loans_for_gsheet(transactions, accounts)

    print("\n=== LOANS GIVEN (you lent money) ===")
    for row in loans_given_rows[1:]:  # skip header
        counterparty, date, amount, status, due_date, note = row
        print(
            f"{date} | {counterparty} | {amount:.2f} ₽ | {status} | {due_date} | {note}"
        )
    if len(loans_given_rows) == 1:
        print("no loans given found")

    print("\n=== LOANS TAKEN (you borrowed) ===")
    for row in loans_taken_rows[1:]:
        counterparty, date, amount, status, due_date, note = row
        print(
            f"{date} | {counterparty} | {amount:.2f} ₽ | {status} | {due_date} | {note}"
        )
    if len(loans_taken_rows) == 1:
        print("no loans taken found")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="print loans given/taken from zenmoney for debugging purposes",
    )
    parser.add_argument(
        "--days",
        type=int,
        default=365,
        help="how many days back to fetch transactions (default: 365)",
    )
    args = parser.parse_args()

    main(args.days)
