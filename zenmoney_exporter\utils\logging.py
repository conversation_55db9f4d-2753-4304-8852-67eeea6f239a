from loguru import logger
import sys


def get_logger(level: str = "INFO"):
    """returns a configured loguru logger following project style-guide"""
    # remove pre-existing handlers to avoid duplicates when re-importing
    logger.remove()
    logger.add(
        sink=sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> - <level>{level}</level> - <cyan>{message}</cyan>",
        colorize=True,
        level=level.upper(),
    )
    return logger
