from __future__ import annotations

from zenmoney import Request, Diff  # type: ignore
from loguru import logger


class ZenMoneyClient:
    """simple wrapper around the zenmoney sdk for fetching transactions and tags."""

    def __init__(self, api_key: str):
        self.request = Request(api_key)

    def fetch_transactions(self, server_timestamp: int):
        logger.info(f"fetching data since timestamp {server_timestamp}")
        diff_request = Diff(serverTimestamp=server_timestamp)
        return self.request.diff(diff_request)

    def get_transactions(self, days_back: int, fetch_all_tags: bool = True):
        """replicates logic from legacy ``get_zenmoney_transactions`` but returns raw data.

        returns:
            tuple(transactions, accounts_dict, tags_dict)
        """
        from datetime import datetime, timedelta
        from zenmoney.exception import ZenMoneyException  # type: ignore

        now = datetime.now()
        start_date = now - timedelta(days=days_back)
        server_timestamp = int(start_date.timestamp())

        logger.info(
            f"connecting to zenmoney api… requesting diff since {server_timestamp}"
        )

        try:
            diff_request = Diff(serverTimestamp=server_timestamp)
            diff_response = self.request.diff(diff_request)

            transactions = getattr(diff_response, "transaction", [])
            accounts = {acc.id: acc for acc in getattr(diff_response, "account", [])}

            tags = {tag.id: tag.title for tag in getattr(diff_response, "tag", [])}

            if fetch_all_tags:
                logger.info("fetching all historical tags…")
                all_tags_request = Diff(serverTimestamp=1)
                all_tags_response = self.request.diff(all_tags_request)
                all_tags_data = {
                    tag.id: tag.title for tag in getattr(all_tags_response, "tag", [])
                }
                tags.update(all_tags_data)

            logger.info(f"fetched {len(transactions)} transactions from zenmoney api")
            return transactions, accounts, tags

        except ZenMoneyException as exc:
            logger.error(f"zenmoney api error: {exc}")
        except Exception as exc:
            logger.error(f"unknown error fetching data: {exc}")

        return None, None, None
