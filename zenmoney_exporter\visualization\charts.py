from __future__ import annotations

from datetime import datetime
import googleapiclient.discovery
from google.oauth2 import service_account
from loguru import logger


def _delete_existing_charts(service, spreadsheet_key: str, sheet_id: int) -> None:
    """delete all embedded charts in the given sheet.

    google sheets retains embedded objects (charts, images) independently of the cell
    contents. if we clear the worksheet data and add new charts without removing the
    old ones, duplicate visuals appear. this helper enumerates `charts` for the
    target sheet and issues `deleteEmbeddedObject` requests for each chart id.

    the operation is silent if the sheet has no charts or if any chart ids cannot be
    removed (google api ignores missing object ids in delete requests).
    """

    try:
        # fetch minimal metadata for charts; restrict fields for performance
        spreadsheet = (
            service.spreadsheets()
            .get(
                spreadsheetId=spreadsheet_key,
                includeGridData=False,
                fields="sheets(properties(sheetId),charts(chartId))",
            )
            .execute()
        )

        chart_ids: list[int] = []
        for sheet in spreadsheet.get("sheets", []):
            props = sheet.get("properties", {})
            if props.get("sheetId") != sheet_id:
                continue
            for chart in sheet.get("charts", []):
                cid = chart.get("chartId")
                if cid is not None:
                    chart_ids.append(cid)

        if not chart_ids:
            logger.debug("no existing charts to delete on sheet id {}", sheet_id)
            return

        delete_reqs = [{"deleteEmbeddedObject": {"objectId": cid}} for cid in chart_ids]

        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key,
            body={"requests": delete_reqs},
        ).execute()

        logger.info(
            "deleted {} existing charts from sheet id {}", len(chart_ids), sheet_id
        )
    except Exception as exc:
        # log and proceed; failure to delete should not block subsequent chart creation
        logger.error(
            "failed to delete existing charts on sheet id {}: {}", sheet_id, exc
        )


def _clear_category_table_borders(service, spreadsheet_key: str, sheet_id: int) -> None:
    """remove all borders from the previous category table (columns m & n)."""

    # range estimate: rows 0–400 covers typical worksheet height and avoids needing row count
    try:
        none_border = {"style": "NONE"}

        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key,
            body={
                "requests": [
                    {
                        "updateBorders": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": 0,
                                "endRowIndex": 400,
                                "startColumnIndex": 12,  # column m (0-indexed)
                                "endColumnIndex": 14,  # up to n (exclusive)
                            },
                            "top": none_border,
                            "bottom": none_border,
                            "left": none_border,
                            "right": none_border,
                            "innerHorizontal": none_border,
                            "innerVertical": none_border,
                        }
                    }
                ]
            },
        ).execute()
        logger.debug("cleared previous category table borders on sheet id {}", sheet_id)
    except Exception as exc:
        logger.error(
            "failed to clear category table borders on sheet id {}: {}", sheet_id, exc
        )


def add_monthly_charts(
    service, spreadsheet_key: str, worksheet_name: str, sheet_id: int, data
):
    """adds income/outcome column chart and categories pie chart to monthly worksheets.

    this function creates two charts at the top of each monthly worksheet:
    1. column (bar) chart showing income vs outcome trends
    2. pie chart showing top expense categories

    args:
        service: google sheets api service
        spreadsheet_key: id of the google spreadsheet
        worksheet_name: name of the worksheet to add charts to
        sheet_id: id of the sheet within the spreadsheet
        data: transaction data as a list of lists
    """
    if not data or len(data) <= 1:
        logger.info(f"not enough data to create charts for {worksheet_name}")
        return

    # ensure the worksheet is free of old charts before adding new ones
    _delete_existing_charts(service, spreadsheet_key, sheet_id)

    # clear previous category table borders so stale styling is removed
    _clear_category_table_borders(service, spreadsheet_key, sheet_id)

    try:
        # use a grid range that spans all columns (omit column indices) to guarantee that
        # any merged regions within the first 200 rows are fully contained in the range.
        # the google sheets api considers omitted boundaries as "sheet edge".

        charts_request_body_pre = {
            "requests": [
                {
                    "unmergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 0,
                            "endRowIndex": 200,
                            # intentionally omit startColumnIndex/endColumnIndex for full-width
                        }
                    }
                }
            ]
        }
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key, body=charts_request_body_pre
        ).execute()

        # define column indices for the data we need
        date_col = 0  # column a: date
        income_col = 3  # column d: income
        outcome_col = 5  # column f: outcome
        tags_col = 9  # column j after new currency column

        # need to add padding at the top of the worksheet for charts
        # create padding request - 20 rows at the top
        padding_request = {
            "insertRange": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": 0,
                    "endRowIndex": 20,
                    "startColumnIndex": 0,
                    "endColumnIndex": len(data[0]),
                },
                "shiftDimension": "ROWS",
            }
        }

        # execute the padding request
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key,
            body={"requests": [padding_request]},
        ).execute()
        logger.debug(f"added padding rows at the top of {worksheet_name}")

        # after inserting 20 rows at the top, the data that was at A21 is now at A41
        # we need to move it back to A21 for consistency

        # first, clear the target position A21
        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A21:Z{21+len(data)}",
        ).execute()

        # move the data from A41 (where it ended up after padding) back to A21
        service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A21",
            valueInputOption="RAW",
            body={"values": data},
        ).execute()
        logger.debug(f"moved data to row 21 in {worksheet_name}")

        # clear the old duplicated block that now starts at row 41 **after** we have re-written
        # fresh data to row 21. start clearing from the first row *after* the new data ends to
        # avoid wiping any freshly written rows when len(data) exceeds the 20-row padding.

        data_start_row = 20
        clear_start_row = data_start_row + len(data) + 1  # 1-indexed row number
        clear_end_row = clear_start_row + len(data)  # enough rows to cover old block

        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_key,
            range=f"{worksheet_name}!A{clear_start_row}:Z{clear_end_row}",
        ).execute()

        # data now starts at row 21 (0-indexed: row 20)
        data_start_row = 20

        # prepare to create charts
        charts_request_body = {"requests": []}

        # adjust column widths based on content
        column_widths = [
            {"column": 0, "width": 90},  # date (a) - 10 chars
            {"column": 1, "width": 70},  # type (b) - 7 chars
            {"column": 2, "width": 120},  # income_account (c) - 12-15 chars
            {"column": 3, "width": 90},  # income (d) - 5-6 digits
            {"column": 4, "width": 120},  # outcome_account (e) - 12-15 chars
            {"column": 5, "width": 90},  # outcome (f) - 6 digits
            {"column": 6, "width": 70},  # currency
            {"column": 7, "width": 220},  # payee (g) - 30-35 chars
            {"column": 8, "width": 180},  # comment (h) - variable
            {"column": 9, "width": 120},  # tags (i) - 10-15 chars
            {"column": 10, "width": 280},  # transaction_id (j) - 36 chars (uuid)
            {"column": 11, "width": 130},  # created_at (k) - 17 chars
            {"column": 12, "width": 130},  # changed_at (l) - 17 chars
        ]

        # create column width adjustment requests
        column_requests = []
        for col_def in column_widths:
            column_requests.append(
                {
                    "updateDimensionProperties": {
                        "range": {
                            "sheetId": sheet_id,
                            "dimension": "COLUMNS",
                            "startIndex": col_def["column"],
                            "endIndex": col_def["column"] + 1,
                        },
                        "properties": {"pixelSize": col_def["width"]},
                        "fields": "pixelSize",
                    }
                }
            )

        # execute column width adjustments
        if column_requests:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={"requests": column_requests},
            ).execute()
            logger.debug(f"adjusted column widths in {worksheet_name} based on content")

        # validate that we have sufficient numeric data for the column chart
        valid_data_rows = 0
        for row in data[1:]:  # skip header
            try:
                # just check if we have valid rows with data
                if len(row) > max(income_col, outcome_col):
                    valid_data_rows += 1
            except (ValueError, TypeError, IndexError):
                # skip rows with invalid data
                continue

        # define chart dimensions for edge-to-edge 50|50 layout (two charts per row)
        # both charts will share the same height and span half of the visible sheet width.
        # values chosen empirically to fill standard 1920px wide screens without gap while allowing
        # some overflow handling in narrower views (google sheets auto-scrolls horizontally).

        line_chart_width = 655  # width for column chart (previously line)
        pie_chart_width = 781  # same width for symmetry
        line_chart_height = 410
        pie_chart_height = 410

        # only create column chart if we have enough valid data rows
        if valid_data_rows >= 2:  # need at least 2 data points for a meaningful chart
            # 1. column chart for income and outcome over time
            line_chart = {
                "addChart": {
                    "chart": {
                        "spec": {
                            "title": f"По Дате ({worksheet_name})",
                            "titleTextFormat": {"fontFamily": "Inter"},
                            "basicChart": {
                                "chartType": "COLUMN",
                                "legendPosition": "BOTTOM_LEGEND",
                                "axis": [
                                    {
                                        "position": "BOTTOM_AXIS",
                                        "title": "date",
                                        "format": {"fontFamily": "Inter"},
                                    },
                                    {
                                        "position": "LEFT_AXIS",
                                        "title": "amount (₽)",
                                        "format": {"fontFamily": "Inter"},
                                    },
                                ],
                                "domains": [
                                    {
                                        "domain": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,  # include header for labels
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": date_col,
                                                        "endColumnIndex": date_col + 1,
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                "series": [
                                    {
                                        "series": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,  # include header for labels
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": income_col,
                                                        "endColumnIndex": income_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",
                                        "color": {
                                            "red": 0.2,
                                            "green": 0.6,
                                            "blue": 0.2,
                                        },
                                    },
                                    {
                                        "series": {
                                            "sourceRange": {
                                                "sources": [
                                                    {
                                                        "sheetId": sheet_id,
                                                        "startRowIndex": data_start_row,  # include header for labels
                                                        "endRowIndex": data_start_row
                                                        + len(data),
                                                        "startColumnIndex": outcome_col,
                                                        "endColumnIndex": outcome_col
                                                        + 1,
                                                    }
                                                ]
                                            }
                                        },
                                        "targetAxis": "LEFT_AXIS",
                                        "color": {
                                            "red": 0.8,
                                            "green": 0.2,
                                            "blue": 0.2,
                                        },
                                    },
                                ],
                                "headerCount": 1,
                            },
                        },
                        "position": {
                            "overlayPosition": {
                                "anchorCell": {
                                    "sheetId": sheet_id,
                                    "rowIndex": 0,
                                    "columnIndex": 0,
                                },
                                "widthPixels": line_chart_width,
                                "heightPixels": line_chart_height,
                            }
                        },
                    }
                }
            }
            charts_request_body["requests"].append(line_chart)
        else:
            logger.info(
                f"skipping column chart for {worksheet_name}: insufficient numeric data (found {valid_data_rows} valid rows)"
            )

        # 2. aggregate transactions by tags for pie chart
        # first we need to count/sum by category
        tags_aggregation_data = {}
        valid_tag_rows = 0

        for row in data[1:]:  # skip header
            if not row[tags_col] or len(row) <= tags_col:  # skip entries with no tags
                continue

            try:
                # handle multiple tags (comma-separated)
                row_tags = row[tags_col].split(", ")
                outcome_val = float(row[outcome_col]) if row[outcome_col] else 0

                if outcome_val <= 0:  # only include expenses
                    continue

                valid_tag_rows += 1

                # split the outcome evenly between multiple tags
                tag_value = outcome_val / len(row_tags)

                for tag_item in row_tags:
                    if tag_item in tags_aggregation_data:
                        tags_aggregation_data[tag_item] += tag_value
                    else:
                        tags_aggregation_data[tag_item] = tag_value
            except (ValueError, TypeError, IndexError):
                # skip rows with invalid data
                continue

        # filter to top 5 categories for readability
        top_tags = sorted(
            tags_aggregation_data.items(), key=lambda x: x[1], reverse=True
        )[:5]

        # create a separate area for pie chart data at the top of the sheet
        # use a fixed area for consistency, starting at row 2, column 14 (n)
        pie_data_start_row = 1
        pie_data_col = 14  # column o after adding separator column n

        # clear the area regardless of whether we'll create a chart
        # this ensures we don't have leftover data from previous runs
        clear_request = {
            "updateCells": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 10,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "fields": "userEnteredValue",
            }
        }
        charts_request_body["requests"].append(clear_request)

        # check if we have enough valid tag data for a real pie chart
        has_enough_data = valid_tag_rows >= 2 and len(top_tags) >= 1

        # always add the header row
        chart_data_header_req = {
            "updateCells": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 1,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "rows": [
                    {
                        "values": [
                            {"userEnteredValue": {"stringValue": "категория"}},
                            {"userEnteredValue": {"stringValue": "сумма"}},
                        ]
                    }
                ],
                "fields": "userEnteredValue",
            }
        }
        charts_request_body["requests"].append(chart_data_header_req)

        # if we have enough data, use real tag data
        if has_enough_data:
            # add data rows for categories
            chart_data_values = []
            for tag_item, amount_val in top_tags:
                chart_data_values.append(
                    {
                        "values": [
                            {"userEnteredValue": {"stringValue": tag_item}},
                            {"userEnteredValue": {"numberValue": amount_val}},
                        ]
                    }
                )

            # add category data
            chart_data_values_req = {
                "updateCells": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 1 + len(top_tags),
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": chart_data_values,
                    "fields": "userEnteredValue",
                }
            }
            charts_request_body["requests"].append(chart_data_values_req)
        else:
            # add placeholder data when there's not enough real data
            # this ensures the chart can be created without errors
            logger.info(f"using placeholder data for pie chart in {worksheet_name}")
            placeholder_data = [
                {
                    "values": [
                        {"userEnteredValue": {"stringValue": "no data"}},
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
                {
                    "values": [
                        {"userEnteredValue": {"stringValue": "add transactions"}},
                        {"userEnteredValue": {"numberValue": 1}},
                    ]
                },
            ]

            placeholder_request = {
                "updateCells": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 3,
                        "startColumnIndex": pie_data_col,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "rows": placeholder_data,
                    "fields": "userEnteredValue",
                }
            }
            charts_request_body["requests"].append(placeholder_request)

        # add medium black borders to the category data area (header + data rows or placeholder)
        medium_black = {
            "style": "SOLID",
            "width": 2,
            "color": {"red": 0.0, "green": 0.0, "blue": 0.0},
        }

        border_req = {
            "updateBorders": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row
                    + 1
                    + (len(top_tags) if has_enough_data else 3),
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "top": medium_black,
                "bottom": medium_black,
                "left": medium_black,
                "right": medium_black,
                "innerHorizontal": medium_black,
                "innerVertical": medium_black,
            }
        }
        charts_request_body["requests"].append(border_req)

        # apply inter font to category data area (header + rows)
        font_req = {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row
                    + 1
                    + (len(top_tags) if has_enough_data else 3),
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "cell": {"userEnteredFormat": {"textFormat": {"fontFamily": "Inter"}}},
                "fields": "userEnteredFormat.textFormat.fontFamily",
            }
        }
        charts_request_body["requests"].append(font_req)

        # make header cells of category table bold
        cat_header_bold_req = {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row,
                    "endRowIndex": pie_data_start_row + 1,
                    "startColumnIndex": pie_data_col,
                    "endColumnIndex": pie_data_col + 2,
                },
                "cell": {
                    "userEnteredFormat": {
                        "textFormat": {"bold": True, "fontFamily": "Inter"}
                    }
                },
                "fields": "userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.fontFamily",
            }
        }
        charts_request_body["requests"].append(cat_header_bold_req)

        # make only the monetary value cells in the category data area bold (not the category names)
        if has_enough_data and len(top_tags) > 0:
            cat_value_bold_req = {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": pie_data_start_row + 1,
                        "endRowIndex": pie_data_start_row + 1 + len(top_tags),
                        "startColumnIndex": pie_data_col + 1,
                        "endColumnIndex": pie_data_col + 2,
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "textFormat": {"bold": True, "fontFamily": "Inter"},
                            "numberFormat": {
                                "type": "NUMBER",
                                "pattern": '#,##0.00 "₽"',
                            },
                        }
                    },
                    "fields": "userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.fontFamily,userEnteredFormat.numberFormat",
                }
            }
            charts_request_body["requests"].append(cat_value_bold_req)

        # ensure the first data row of the category table (just below the title) has category name unbolded while keeping value bold
        unbold_header_req = {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": pie_data_start_row + 1,
                    "endRowIndex": pie_data_start_row + 2,
                    "startColumnIndex": pie_data_col,  # only category name column
                    "endColumnIndex": pie_data_col + 1,
                },
                "cell": {"userEnteredFormat": {"textFormat": {"bold": False}}},
                "fields": "userEnteredFormat.textFormat.bold",
            }
        }
        charts_request_body["requests"].append(unbold_header_req)

        # --- monthly total summary table (below category table) ---
        # calculate dynamic starting row: category table header (row 0) + data rows + 6 spacing rows
        summary_start_row = (
            pie_data_start_row
            + 1
            + (len(top_tags) if has_enough_data else 3)
            + 6  # spacing rows increased from 3 to 6
        )
        summary_label_row = summary_start_row
        summary_value_row = summary_start_row + 1
        summary_end_row = summary_start_row + 2  # exclusive for range convenience

        # outcome and income summary rows
        outcome_label_row = summary_end_row
        outcome_value_row = outcome_label_row + 1
        outcome_end_row = outcome_label_row + 2
        income_label_row = outcome_end_row
        income_value_row = income_label_row + 1
        income_end_row = income_label_row + 2

        # unmerge and clear all 3 summary table areas (existing + outcome + income)
        for start, end in [
            (summary_label_row, summary_end_row),
            (outcome_label_row, outcome_end_row),
            (income_label_row, income_end_row),
        ]:
            charts_request_body["requests"].append(
                {
                    "unmergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": start,
                            "endRowIndex": end,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        }
                    }
                }
            )
            charts_request_body["requests"].append(
                {
                    "updateCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": start,
                            "endRowIndex": end,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "fields": "userEnteredValue",
                    }
                }
            )

        # helper for label and value row creation
        def summary_table(label_row, value_row, label, formula, bold_value=False):
            # merge label row
            charts_request_body["requests"].append(
                {
                    "mergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": label_row,
                            "endRowIndex": label_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "mergeType": "MERGE_ALL",
                    }
                }
            )
            # set label
            charts_request_body["requests"].append(
                {
                    "updateCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": label_row,
                            "endRowIndex": label_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "rows": [
                            {"values": [{"userEnteredValue": {"stringValue": label}}]}
                        ],
                        "fields": "userEnteredValue",
                    }
                }
            )
            # merge value row
            charts_request_body["requests"].append(
                {
                    "mergeCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": value_row,
                            "endRowIndex": value_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "mergeType": "MERGE_ALL",
                    }
                }
            )
            # set formula
            charts_request_body["requests"].append(
                {
                    "updateCells": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": value_row,
                            "endRowIndex": value_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "rows": [
                            {
                                "values": [
                                    {"userEnteredValue": {"formulaValue": formula}}
                                ]
                            }
                        ],
                        "fields": "userEnteredValue",
                    }
                }
            )
            # apply number format
            charts_request_body["requests"].append(
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": value_row,
                            "endRowIndex": value_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "numberFormat": {
                                    "type": "NUMBER",
                                    "pattern": '#,##0.00 "₽"',
                                },
                                "textFormat": {"fontFamily": "Inter"},
                            }
                        },
                        "fields": "userEnteredFormat.numberFormat,userEnteredFormat.textFormat.fontFamily",
                    }
                }
            )
            # add borders
            charts_request_body["requests"].append(
                {
                    "updateBorders": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": label_row,
                            "endRowIndex": value_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "top": medium_black,
                        "bottom": medium_black,
                        "left": medium_black,
                        "right": medium_black,
                        "innerHorizontal": medium_black,
                        "innerVertical": medium_black,
                    }
                }
            )
            # font family for both rows
            charts_request_body["requests"].append(
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": label_row,
                            "endRowIndex": value_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "cell": {
                            "userEnteredFormat": {"textFormat": {"fontFamily": "Inter"}}
                        },
                        "fields": "userEnteredFormat.textFormat.fontFamily",
                    }
                }
            )
            # bold label row
            charts_request_body["requests"].append(
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": label_row,
                            "endRowIndex": label_row + 1,
                            "startColumnIndex": pie_data_col,
                            "endColumnIndex": pie_data_col + 2,
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "textFormat": {"bold": True, "fontFamily": "Inter"}
                            }
                        },
                        "fields": "userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.fontFamily",
                    }
                }
            )
            # bold value row if bold_value is True
            if bold_value:
                charts_request_body["requests"].append(
                    {
                        "repeatCell": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": value_row,
                                "endRowIndex": value_row + 1,
                                "startColumnIndex": pie_data_col,
                                "endColumnIndex": pie_data_col + 2,
                            },
                            "cell": {
                                "userEnteredFormat": {
                                    "textFormat": {"bold": True, "fontFamily": "Inter"}
                                }
                            },
                            "fields": "userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.fontFamily",
                        }
                    }
                )

        # main total table
        summary_table(
            summary_label_row,
            summary_value_row,
            f"ИТОГО ЗА {worksheet_name}",
            "=sum(D22:D)+sum(F22:F)",
            bold_value=True,
        )
        # outcome total table (bold value row)
        summary_table(
            outcome_label_row,
            outcome_value_row,
            f"ИТОГО РАСХОД – {worksheet_name}",
            '=sumif(F22:F, ">0")',
            bold_value=True,
        )
        # income total table (bold value row)
        summary_table(
            income_label_row,
            income_value_row,
            f"ИТОГО ДОХОД – {worksheet_name}",
            '=sumif(D22:D, ">0")',
            bold_value=True,
        )

        # --- end monthly total summary tables ---

        # add narrow separator column (index 13, column n) width ~10 px
        charts_request_body["requests"].append(
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": pie_data_col - 1,  # separator column (13)
                        "endIndex": pie_data_col,
                    },
                    "properties": {"pixelSize": 10},
                    "fields": "pixelSize",
                }
            }
        )

        # set column widths for category table columns (o & p)
        cat_col_widths = [160, 100]
        for idx, pixel in enumerate(cat_col_widths):
            charts_request_body["requests"].append(
                {
                    "updateDimensionProperties": {
                        "range": {
                            "sheetId": sheet_id,
                            "dimension": "COLUMNS",
                            "startIndex": pie_data_col + idx,
                            "endIndex": pie_data_col + idx + 1,
                        },
                        "properties": {"pixelSize": pixel},
                        "fields": "pixelSize",
                    }
                }
            )

        # always create the pie chart (with either real or placeholder data)
        pie_chart_title = f"По Категориям ({worksheet_name})"
        if not has_enough_data:
            pie_chart_title += " (not enough data)"

        pie_chart = {
            "addChart": {
                "chart": {
                    "spec": {
                        "title": pie_chart_title,
                        "titleTextFormat": {"fontFamily": "Inter"},
                        "pieChart": {
                            "legendPosition": "LABELED_LEGEND",
                            "domain": {
                                "sourceRange": {
                                    "sources": [
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col,
                                            "endColumnIndex": pie_data_col + 1,
                                        }
                                    ]
                                }
                            },
                            "series": {
                                "sourceRange": {
                                    "sources": [
                                        {
                                            "sheetId": sheet_id,
                                            "startRowIndex": pie_data_start_row,
                                            "endRowIndex": pie_data_start_row
                                            + 1
                                            + (len(top_tags) if has_enough_data else 2),
                                            "startColumnIndex": pie_data_col + 1,
                                            "endColumnIndex": pie_data_col + 2,
                                        }
                                    ]
                                }
                            },
                        },
                    },
                    "position": {
                        "overlayPosition": {
                            "anchorCell": {
                                "sheetId": sheet_id,
                                "rowIndex": 0,
                                "columnIndex": 7,
                            },
                            "widthPixels": pie_chart_width,
                            "heightPixels": pie_chart_height,
                        }
                    },
                }
            }
        }
        charts_request_body["requests"].append(pie_chart)

        # execute the batch update to add the charts
        if charts_request_body["requests"]:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key, body=charts_request_body
            ).execute()
            logger.info(f"successfully added charts to {worksheet_name}")

        # re-apply header formatting after moving data
        # Only format the header row (first row of data at data_start_row)
        header_format_request = {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": data_start_row,  # row 21 (0-indexed: 20)
                    "endRowIndex": data_start_row + 1,  # only the header row
                    "startColumnIndex": 0,
                    "endColumnIndex": len(data[0]),
                },
                "cell": {
                    "userEnteredFormat": {
                        "textFormat": {"bold": True, "fontFamily": "Inter"}
                    }
                },
                "fields": "userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.fontFamily",
            }
        }
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key, body={"requests": [header_format_request]}
        ).execute()
        logger.debug(
            f"reapplied header formatting to row {data_start_row + 1} in {worksheet_name}"
        )

        # ensure all transaction data rows below the header are not bold (clears residual bold formatting
        # that may accumulate from repeated padding insertions).
        unbold_rows_request = {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": data_start_row + 1,
                    "endRowIndex": data_start_row + len(data),
                    "startColumnIndex": 0,
                    "endColumnIndex": len(data[0]),
                },
                "cell": {"userEnteredFormat": {"textFormat": {"bold": False}}},
                "fields": "userEnteredFormat.textFormat.bold",
            }
        }
        service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_key, body={"requests": [unbold_rows_request]}
        ).execute()
        logger.debug(
            "cleared bold formatting from transaction data rows in {}", worksheet_name
        )

        # apply currency number format (with ₽ symbol) to income (column d) and outcome (column f)
        # skip header row by starting at data_start_row + 1; apply to all data rows
        currency_format_requests = []
        for currency_col in (income_col, outcome_col):
            currency_format_requests.append(
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": data_start_row + 1,
                            "endRowIndex": data_start_row + len(data),
                            "startColumnIndex": currency_col,
                            "endColumnIndex": currency_col + 1,
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "numberFormat": {
                                    "type": "NUMBER",
                                    "pattern": '#,##0.00 "₽"',
                                }
                            }
                        },
                        "fields": "userEnteredFormat.numberFormat",
                    }
                }
            )

        if currency_format_requests:
            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_key,
                body={"requests": currency_format_requests},
            ).execute()

    except Exception as e:
        logger.error(f"failed to add charts to {worksheet_name}: {e}")
        import traceback

        logger.error(f"detailed error: {traceback.format_exc()}")
