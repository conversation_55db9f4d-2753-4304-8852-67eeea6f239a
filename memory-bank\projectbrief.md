# zenmoney to google sheets exporter

## project brief

this tool exports transactions from zenmoney to google sheets. it acts as a data pipeline connecting financial transaction data from zenmoney to a google spreadsheet for analysis and visualization.

## key features

-   fetches transactions from zenmoney api
-   formats and uploads data to google sheets
-   uses service account authentication for google sheets
-   handles errors gracefully
-   organizes transactions by month into separate worksheets
-   creates detailed monthly charts for income/outcome and expense categories
-   retrieves all historical tags to properly categorize transactions

## core components

-   **main.py**: core application script that handles data fetching and processing
-   **clean_spreadsheet.py**: utility for cleaning spreadsheet data
-   **save_data.py**: handles data saving operations
-   **credentials/**: directory containing authentication credentials
    -   **config.yaml**: configuration file with api keys and settings
    -   **account.json**: google service account credentials

## technology stack

-   **python**: primary programming language
-   **zenmoney api**: source of transaction data
-   **google sheets api**: destination for processed data
-   **gspread**: python library for google sheets interaction
-   **loguru**: enhanced logging

## setup requirements

1. zenmoney api key
2. google service account credentials
3. python environment with required dependencies

## current status

functional application that successfully exports data from zenmoney to google sheets with appropriate formatting and visualization.
