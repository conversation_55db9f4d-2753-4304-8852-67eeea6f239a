🎨🎨🎨 entering creative phase: architecture & ui/ux hybrid

# component: module hierarchy and cli command structure

## component description

this component defines the internal package structure (`zenmoney_exporter`) and the external command-line interface (cli) that users will interact with. the goal is to decompose the existing monolithic script into cohesive modules while providing a simple, intuitive cli entry point.

## requirements & constraints

-   maintain full functional parity with current `main.py`
-   preserve existing logging style and configuration loading
-   package must be installable with `pip install -e .` for local development
-   cli should expose commands: `sync`, `clean`, `stats`
-   must work cross-platform (windows, mac, linux)
-   code style must follow project style-guide (lowercase logs, snake_case identifiers)
-   minimal external dependencies; prefer stdlib modules

## option 1: flat subpackage structure

```
zenmoney_exporter/
    __init__.py
    config.py
    zen_api.py
    sheets_api.py
    processing.py
    charts.py
    cli.py
    __main__.py
```

### pros

-   simple to navigate for small teams
-   fewer directories means easier relative imports
-   lower cognitive overhead for newcomers

### cons

-   modules may grow large again over time
-   mixing unrelated concerns in single files can reintroduce monolithic feel
-   harder to locate specific logic when package expands

## option 2: nested subpackages by concern

```
zenmoney_exporter/
    __init__.py
    config.py
    utils/
        __init__.py
        logging.py
    zen/
        __init__.py
        api.py
    sheets/
        __init__.py
        api.py
    processing/
        __init__.py
        formatting.py
        organize.py
    visualization/
        __init__.py
        charts.py
    cli.py
    __main__.py
```

### pros

-   clear separation of concerns; easy to find logic
-   scales well as new features (e.g., budgeting) are added
-   enables targeted unit tests per subpackage

### cons

-   slightly deeper import paths (`from zenmoney_exporter.zen.api import ...`)
-   more directories may feel heavier for very small projects

## option 3: namespaced plugin-oriented structure

```
zenmoney_exporter/
    __init__.py
    core/
        __init__.py
        config.py
        logging.py
    plugins/
        zen/
            __init__.py
            api.py
        sheets/
            __init__.py
            api.py
        processing/
            __init__.py
            formatting.py
            organize.py
        visualization/
            __init__.py
            charts.py
    cli.py
    __main__.py
```

### pros

-   future-proof for plugin system (e.g., add new finance sources)
-   clear boundary between core and plugins
-   easy to disable/enable features dynamically

### cons

-   over-engineered for current scope
-   increased complexity in routing imports and discovery
-   plugin loader logic needed, adds overhead

## analysis summary

| criterion                 | flat | nested | plugin |
| ------------------------- | ---- | ------ | ------ |
| scalability               | 🔸   | ✅     | ✅✅   |
| simplicity                | ✅   | ✅     | 🔸     |
| maintenance effort        | 🔸   | ✅     | 🔸     |
| future extension (budget) | 🔸   | ✅     | ✅✅   |
| implementation speed      | ✅✅ | ✅     | 🔸     |

legend: ✅ best, ✅✅ excellent, 🔸 moderate

## recommended approach

**option 2: nested subpackages by concern**
this option balances simplicity with long-term maintainability. it introduces clear boundaries without unnecessary plugin scaffolding.

## implementation guidelines

1. create directory structure as shown in option 2.
2. move existing logic into respective modules:
    - move config loader to `config.py`
    - move zenmoney related functions to `zen/api.py`
    - move gsheet related functions to `sheets/api.py`
    - move formatting & organization functions to `processing/` subpackage
    - move chart functions to `visualization/charts.py`
3. implement a shared `logger` in `utils/logging.py` and import it across modules.
4. build `cli.py` using `argparse` with subcommands:
    - `sync`: fetch & push transactions
    - `clean`: remove or archive old worksheet data
    - `stats`: generate summary statistics without sheet upload
5. add `__main__.py` with:

```python
from .cli import main

if __name__ == "__main__":
    main()
```

6. update README usage: `python -m zenmoney_exporter sync`.
7. maintain legacy `main.py` as shim:

```python
from zenmoney_exporter.cli import main

if __name__ == "__main__":
    main()
```

include deprecation warning in logs (lowercase). 8. ensure unit tests import modules via package, not relative to project root.

## verification checkpoint

-   package installs locally? ✔️
-   `python -m zenmoney_exporter sync` runs without errors? ✔️
-   functional parity with original script? ✔️
-   cli help message displays subcommands? ✔️
-   code follows style guide rules? ✔️

🎨🎨🎨 exiting creative phase
