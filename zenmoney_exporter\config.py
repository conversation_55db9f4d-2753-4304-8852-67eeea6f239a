import os
from pathlib import Path
import yaml
from loguru import logger

CONFIG_ENV_VAR = "ZENMONEY_EXPORTER_CONFIG"
DEFAULT_CONFIG_PATH = (
    Path(__file__).resolve().parent.parent / "credentials" / "config.yaml"
)


def load_config(custom_path: str | None = None):
    """load configuration from yaml file following same logic as original script.

    order of precedence:
    1. explicit ``custom_path`` argument
    2. environment variable ``ZENMONEY_EXPORTER_CONFIG``
    3. default path ``credentials/config.yaml`` relative to project root
    """
    config_path = (
        Path(custom_path).expanduser().resolve()
        if custom_path
        else Path(os.getenv(CONFIG_ENV_VAR, DEFAULT_CONFIG_PATH))
    )

    if not config_path.exists():
        logger.error(f"pizdec! config file not found at: {config_path}")
        return None

    try:
        with config_path.open("r", encoding="utf-8") as fp:
            return yaml.safe_load(fp)
    except yaml.YAMLError as exc:
        logger.error(f"blyat! error parsing config.yaml: {exc}")
    except Exception as exc:
        logger.error(f"unknown error loading config: {exc}")

    return None
