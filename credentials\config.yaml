# zenmoney to google sheets configuration file
# like main pipe connection schematic - all important details in one place

# zenmoney api details
zenmoney:
  api_key: "I2DZBz69OQXyt31HImQRfUVI6kOHas"

# google sheets credentials and details
google_sheets:
  # path to service account credentials json
  credentials_path: "credentials/account.json"
  # spreadsheet key (preferred) or name
  spreadsheet_key: "1se7sf6jgwMOLZ6IIWxySaxOQ98zZmDJwYnCXuoIzLxY"
  # worksheet name
  worksheet_name: "ГЛАВНЫЙ"

# app settings
settings:
  # how many days of transactions to fetch
  days_to_fetch: 30
  # logging level
  log_level: "INFO"
