# tasks

## current van mode activation (2025-06-15)

### platform detection checkpoint

-   [x] verify platform detection: windows 10.0.26100
-   [x] verify python environment: python 3.13.3
-   [x] verify package manager: uv 0.7.9
-   [x] memory bank exists and properly structured
-   [x] basic file verification passed

### dependency verification checkpoint

-   [x] verify required dependencies: all present (gspread, loguru, zenmoney, pyyaml, google-api-python-client, pytest)
-   [x] verify project structure: refactored module hierarchy with zenmoney_exporter package
-   [x] verify credentials: config.yaml contains api keys and paths

## previous tasks

-   [x] verify platform detection: windows 11 pro
-   [x] verify python environment: python 3.13.3
-   [x] verify package manager: uv
-   [x] verify required dependencies: all present (gspread, loguru, zenmoney, pyyaml, google-api-python-client, pytest)
-   [x] verify project structure: refactored module hierarchy with zenmoney_exporter package
-   [x] verify credentials: config.yaml contains api keys and paths
-   [x] determine task complexity: level 1 - quick bug fix (npc interaction handler)

### task analysis

-   implement a simple function `activate_van_mode()` to handle the "start van mode" command in an npc interaction system
-   the function should return an appropriate response message when van mode is activated
-   this is a level 1 task (quick bug fix) as it's a small, isolated change with minimal complexity

### implementation plan

1. create a new file for the npc interaction handler if it doesn't exist yet
2. implement the `activate_van_mode()` function
3. ensure it returns an appropriate response message
4. test the function with the sample command

## van mode initialization

-   [x] verify project setup
-   [x] create memory bank structure
-   [x] document project context
-   [x] document technical details
-   [x] identify system patterns
-   [x] establish product context
-   [x] establish active context

## complexity determination

-   project complexity: **level 3** - intermediate feature requiring codebase refactor into advanced module hierarchy
-   primary functionality is complete and working but maintainability suffers due to monolithic script
-   refactor will improve readability, testability, and extensibility

## potential future tasks

-   [x] implement scheduled automatic runs
-   [x] add more advanced visualizations
-   [x] develop budget tracking features
-   [x] integrate with additional financial data sources
-   [x] create notification system for unusual spending patterns
-   [x] add comprehensive unit test suite
-   [x] optimize performance for larger transaction sets

## advanced file hierarchy refactor plan

### requirements analysis

-   restructure code into a cohesive python package `zenmoney_exporter`
-   split `main.py` into logical modules: `config`, `zen_api`, `gsheets_api`, `data_processing`, `visualization`, `cli`
-   ensure backward-compatible entry point via `python -m zenmoney_exporter` and `__main__.py`
-   maintain existing functionality and logging behavior
-   update imports and path references accordingly

### components affected

-   `main.py` (to be decomposed)
-   `clean_spreadsheet.py`, `save_data.py` (to be integrated or adapted)
-   credentials handling and config loading
-   documentation and setup scripts

### architecture considerations

-   package root: `zenmoney_exporter/`
-   submodules:
    -   `config.py`: manage configuration loading
    -   `zen/api.py`: wrapper around zenmoney sdk
    -   `sheets/api.py`: google sheets interaction
    -   `processing/formatting.py`, `processing/organize.py`: data transformations
    -   `visualization/charts.py`: chart creation helpers
    -   `cli.py`: command-line interface with argparse
-   shared `logger` instance in `utils/logging.py`

### implementation strategy

1. create package skeleton with `__init__.py` files
2. move related function blocks from `main.py` into corresponding modules
3. adjust imports and ensure relative paths work
4. adapt `clean_spreadsheet.py` and `save_data.py` into package as needed
5. create `__main__.py` to launch cli entry point
6. update `requirements.txt` if new dependencies introduced (argparse is stdlib)
7. run linting and unit tests to verify behavior

### detailed steps

-   [x] scaffold package directories and init files
-   [x] extract config loading into `config.py`
-   [x] migrate zenmoney api calls into `zen/api.py`
-   [x] migrate gsheet interactions into `sheets/api.py`
-   [x] migrate data formatting to `processing/formatting.py`
-   [x] migrate monthly organization logic to `processing/organize.py`
-   [x] migrate chart creation to `visualization/charts.py`
-   [x] implement unified logging in `utils/logging.py`
-   [x] create `cli.py` with argparse commands (`sync`, `clean`, `stats`)
-   [x] implement `__main__.py` that proxies to cli
-   [x] update scripts in `README.md`
-   [x] run functional tests on sample data
-   [x] reflection completed 2025-06-15
-   [x] archived 2025-06-15 → see `memory-bank/archive/archive-loan_tables.md`

### dependencies

-   reuse existing dependencies; no new external packages anticipated

### challenges & mitigations

-   risk of breaking imports → implement incremental migration with tests
-   maintaining backward compatibility → provide legacy `main.py` shim temporarily
-   large monolithic script complexity → refactor in stages per module

### creative phase components

-   design of the module hierarchy and cli command structure (requires creative decision; flag for creative phase)

### testing strategy

-   run existing functionality after each migration step
-   create minimal unit tests for config loading and api wrappers

---

## checklist

-   [x] package scaffold created
-   [x] modules populated
-   [x] imports updated
-   [x] cli entry point operational
-   [x] documentation updated # readme updated with new cli & testing instructions
-   [x] functional parity verified
-   [x] unit tests passing # pytest suite passes
-   [ ] remove legacy script once stable

## reflection status

-   reflection completed 2025-06-13

## archive status

-   archived 2025-06-13 → see `memory-bank/archive/archive-module_hierarchy_cli.md`

## task completion

-   status: ✅ completed
-   charts successfully restored and working as expected
-   added `--no-charts` option to sync command for testing
-   fixed line chart date labels by using wider chart dimensions (1000px width)

## bugfix task: restore chart creation functionality (level 2)

determined issue: chart creation was disabled during the refactor. the new `zenmoney_exporter.visualization.charts.add_monthly_charts` function is only a stub and `sheets/api.py` skips chart calls. legacy implementation lives in `deprecated/main.py`.

### overview of changes

-   re-enable automatic chart generation for each monthly worksheet.
-   migrate working code from `deprecated/main.py` into new visualization module.
-   integrate chart call flow back into `zenmoney_exporter.sheets.api`.

### files to modify / add

-   `zenmoney_exporter/visualization/charts.py` (implement real logic)
-   `zenmoney_exporter/sheets/api.py` (invoke chart helper)
-   optional: update tests and docs.

### implementation steps

1. copy chart helper code (`add_general_charts` and related helpers) from `deprecated/main.py` into `visualization/charts.py`, refactor to comply with new package imports and logging.
2. ensure function signature `add_monthly_charts(service, spreadsheet_key, worksheet_name, sheet_id, data)` matches old logic.
3. replace stub in `visualization/charts.py` with full implementation and remove placeholder log.
4. update `sheets/api.py` to call `add_monthly_charts` after data upload when `create_charts` flag is true (default true).
5. write minimal unit tests mocking google sheets api to assert chart request bodies are generated.
6. run functional test on sample sheet to verify charts appear.

### potential challenges

-   adapting batch update request json to new wrappers.
-   large request size limitations.
-   ensuring id references (sheet_id) are correct after refactor.

### testing strategy

-   unit test chart request generation using `pytest` and `unittest.mock`.
-   run `sync` against test spreadsheet and manually verify charts displayed.

### checklist

-   [x] migrate chart code into new module
-   [x] integrate call in sheets/api.py
-   [x] update documentation
-   [x] unit tests passing
-   [x] manual verification in test spreadsheet

### creative phase need

no creative phase required (logic already exists, straightforward migration).

### reflection status (restore charts)

-   reflection completed 2025-06-13

### archive status (restore charts)

-   archived 2025-06-13 → see `memory-bank/archive/archive-restore_charts.md`

## enhancement task: improve transaction table display (level 2)

### overview of changes

-   hide the "transaction id" column from the visible transaction table while retaining the data for internal references.
-   translate all remaining column headers to russian with clear, human-readable names.
-   add uniform borders around the table to match the mini category chart style.
-   set appropriate column widths for optimal readability (date and numeric columns narrower, text columns wider).
-   ensure transactions remain sorted by date in ascending (oldest-first) order.
-   preserve all existing functionality, including charts and data sync logic.

### files to modify / add

-   `zenmoney_exporter/processing/formatting.py` – update header generation with russian translations and ensure transaction id remains for data integrity.
-   `zenmoney_exporter/sheets/api.py` – apply google sheets batch formatting (column hide, borders, column widths) after data upload.
-   optional: `tests/` – add unit tests for header translation and column list integrity.

### implementation steps

1. **translate headers**: in `formatting.py`, create a mapping from english field names to russian (e.g., `{"date": "дата", "income": "сумма"}`) and build the header row using this mapping, keeping `transaction_id` but allowing its header to stay english (it will be hidden).
2. **adjust column order**: maintain existing order so downstream logic is untouched. note the index of `transaction_id` (column j, index 9) for later hiding.
3. **export data**: leave the data rows unchanged to keep full fidelity.
4. **post-upload formatting helper**: add a private method `_apply_transactions_table_formatting(service, spreadsheet_key, sheet_id, num_cols, num_rows)` inside `sheets/api.py` that issues a `batchUpdate` with the following requests:
    - set borders (`updateBorders`) on range a1: last_column last_row using thin grey solid lines.
    - hide the transaction id column via `updateDimensionProperties` (`hiddenByUser: true`).
    - set column widths with reasonable `pixelSize` values (e.g., 90 for date, 120 for amount, 220 for comment).
5. **integrate helper**: in `upload_general_sheet` (and optionally `upload_monthly_sheets`) obtain the sheet id from the worksheet and call the helper immediately after data write.
6. **unit test**: add a test that mocks the sheets service and verifies that the batch request list contains border, hide, and width operations.
7. **documentation**: update `README.md` to mention russian headers and refined table style.

### potential challenges

-   constructing correct google sheets `batchUpdate` bodies without live testing – mitigate by referencing official api examples and existing chart code patterns.
-   ensuring column indices remain correct if header list changes – use a constant or lookup to avoid magic numbers.
-   api quota limits for repeated formatting calls – minimize by batching all requests into a single call per upload.

### testing strategy

-   unit tests for `format_transactions_for_gsheet` to confirm header translation and column count.
-   integration test (mocked) for `_apply_transactions_table_formatting` to ensure expected requests.
-   manual validation in a test spreadsheet to verify borders, hidden column, and header translations appear as intended.

### checklist

-   [x] header translation implemented
-   [x] formatting helper added to sheets/api.py
-   [x] column hiding, borders, and widths applied
-   [x] unit tests updated / added
-   [x] manual spreadsheet verification complete
-   [x] medium black borders applied to all tables

### creative phase need

no creative phase required – purely visual/formatting changes.

## reflection status (transaction table display enhancement)

-   reflection completed 2025-06-13

## task completion (enhancement)

-   status: ✅ completed

## bugfix task: clear charts duplication on data refresh (level 2)

existing issue: when worksheets are cleared and new transactions imported, old charts remain and new charts are added in addition, causing duplicated or layered data visuals. charts must be removed or refreshed before new ones are created.

### overview of changes

-   delete existing embedded charts from a worksheet before adding new charts during every import cycle.
-   ensure cell data clearing removes any chart data references.
-   guarantee that after upload, charts only reflect current transaction data.

### files to modify / add

-   `zenmoney_exporter/visualization/charts.py` – add helper `_delete_existing_charts` and call it at the start of `add_monthly_charts`.
-   `zenmoney_exporter/sheets/api.py` – optionally expose a public method to clear charts for general sheet if chart support added later.
-   `tests/test_charts.py` – add unit tests to verify deletion requests are queued before chart creation.

### implementation steps

1. **discover existing charts**: use `service.spreadsheets().get(includeGridData=false)` to fetch spreadsheet metadata. locate the sheet matching `sheet_id` and gather `chartId` values from the `charts` array.
2. **prepare deletion requests**: build a `deleteEmbeddedObject` request for each chart id.
3. **batch delete**: execute a single `batchUpdate` call to remove all charts for the target worksheet.
4. **refactor**: implement `_delete_existing_charts(service, spreadsheet_key, sheet_id)` in `visualization/charts.py`.
5. **integrate**: call the helper at the very beginning of `add_monthly_charts` and exit early if no charts exist.
6. **general worksheet support**: expose a thin wrapper in `sheets/api.py` should future general sheet charts be enabled.
7. **unit tests**: mock the sheets api to confirm that deletion requests are sent prior to any `addChart` requests.
8. **manual test**: run `sync` twice on a test spreadsheet and confirm only one set of charts appears per worksheet.

### potential challenges & mitigations

-   **api limits**: batching all deletion requests into one call minimizes quota usage.
-   **missing charts property**: handle safely if `charts` array absent by checking with `.get("charts", [])`.
-   **concurrent updates**: keep requests idempotent; deletion of non-existent chart ids ignored by api.

### testing strategy

-   unit test deletion logic with fake metadata containing multiple chart ids.
-   integration test (mocked) ensuring sequence: delete → add.
-   manual spreadsheet verification after two consecutive sync runs.

### checklist

-   [x] `_delete_existing_charts` implemented
-   [x] `add_monthly_charts` integrates deletion
-   [x] unit tests added & passing
-   [x] manual verification in test spreadsheet
-   [x] documentation updated (`README.md` troubleshooting section)

### archive status

-   archived 2025-06-13 → see `memory-bank/archive/archive-clear_charts_duplication.md`

### task completion

-   status: ✅ completed
-   duplicate charts removed; formatting stabilized across all sheets

## reflection status

-   reflection completed 2025-06-13

## enhancement task: replace line chart with column chart (level 2)

### overview of changes

-   convert the existing income/outcome **line chart** on each monthly worksheet to a **column (bar) chart**.
-   keep the same data sources, colors, legends, tooltips, and responsive layout.
-   ensure pie chart and other worksheet logic remain unaffected.

### files to modify

-   `zenmoney_exporter/visualization/charts.py` – update the chart specification from `"chartType": "LINE"` to `"chartType": "COLUMN"`.
-   (optional) `deprecated/main.py` – legacy implementation; update only if still used in any workflow.

### implementation steps

1. scan the codebase for all occurrences of `"chartType": "LINE"` in source files (excluding virtual env).
2. confirm which chart implementations are actively used (primary: `visualization/charts.py`).
3. update the `basicChart.chartType` value from `LINE` to `COLUMN`.
4. verify axis settings remain logical (bottom axis dates, left axis amounts).
5. run the sync command on a test spreadsheet to generate the new chart.
6. visually confirm the column chart renders correctly and matches data values.
7. run unit tests (or create one) to assert that the generated request body now contains `"COLUMN"` instead of `"LINE"`.

### potential challenges & mitigations

-   **axis scaling differences** → confirm google sheets auto-scales column charts similarly; adjust `targetAxis` if needed.
-   **color consistency** → current series explicitly define rgb colors; no change needed.
-   **deprecated code divergence** → if legacy path is unused, skip changes; otherwise apply same update.

### testing strategy

-   functional test: run `python -m zenmoney_exporter.cli sync --sheet test` and inspect the worksheet.
-   unit test: mock google sheets api and assert `chartType == "COLUMN"` in generated json.
-   regression: ensure pie chart creation unaffected and no errors logged.

### checklist

-   [x] identify all line chart specs
-   [x] confirm active usage path
-   [x] update chartType to `COLUMN`
-   [x] verify axis settings and styling
-   [x] run unit tests
-   [x] run functional test and visually inspect
-   [x] update docs if necessary
-   [x] mark task complete

### reflection status

-   reflection completed 2025-06-14

### archive status

-   archived 2025-06-14 → see `memory-bank/archive/archive-replace_line_chart.md`

## enhancement task: add monthly total summary table (level 3)

### overview of changes

-   add a monthly total summary table below the existing categories table with six empty rows spacing for future additions

### requirements analysis

-   core requirements:
    -   [x] position summary table below categories table with adequate spacing
    -   [x] use merged cells for two-column, two-row table structure
    -   [x] display month label in russian uppercase format (e.g., "МАЙ - 2025")
    -   [x] calculate and display the sum of all transactions for the current month dynamically with ruble currency symbol
    -   [x] apply styling consistent with existing table design

### components affected

-   `zenmoney_exporter/processing/organize.py`:
    -   add method to compute current month's total
-   `zenmoney_exporter/sheets/api.py`:
    -   insert rows, merge cells, set formula, apply styling via batchUpdate

### implementation steps

1. [x] determine categories table range and calculate insertion point
2. [x] insert 3-5 empty rows below categories table for spacing
3. [x] merge cells in the first row of the new section and set label "total for [month] - yyyy"
4. [x] merge cells in the second row and set formula to sum transaction range (e.g., `=sum(b2:b)`) dynamically
5. [x] apply formatting (font, alignment, borders) matching existing design
6. [x] update unit tests to mock sheets service and verify batchUpdate requests for insertion, merging, formula, and styling

### creative phases required

-   [x] ⚙️ algorithm design (dynamic formula construction)

### checkpoints

-   [x] requirements verified
-   [x] implementation plan detailed
-   [x] code modifications implemented
-   [x] formatting tests passing

### current status

-   phase: build
-   status: in progress (local tests green; unmerge range adjusted—ready for live test)
-   blockers: await rerun of sync to verify batchUpdate passes

### reflection status

-   reflection completed 2025-06-14

### archive status

-   archived 2025-06-14 → see `memory-bank/archive/archive-add_monthly_total_summary_table.md`

## task completion (enhancement)

-   status: ✅ completed

## enhancement task: add separator column between categories and total tables (level 2)

### overview of changes

-   shift the existing _categories_ table and the _total_ summary table **one column to the right** (from current columns n–o to o–p).
-   insert a new **blank separator column** at column **n** to create visual spacing.
-   set the separator column width to **~10 px** (0.5–1 character) for a slim divider effect.
-   preserve all existing data, formulas, borders, and formatting in both tables.

### current layout analysis

| element               | start column (0-indexed) | letter |
| --------------------- | ------------------------ | ------ |
| categories table      | 13                       | n      |
| total table (summary) | 13                       | n      |

### proposed new layout

| element               | start column (0-indexed) | letter |
| --------------------- | ------------------------ | ------ |
| separator column      | 13                       | n      |
| categories table      | 14                       | o      |
| total table (summary) | 14                       | o      |

### files to modify

-   `zenmoney_exporter/visualization/charts.py` – update `pie_data_col` constant from **13** → **14** and add a dimension property request to set column **13** width to **10 px**.
-   `tests/` – update any unit tests that assert column indices for category data.

### implementation steps

1. locate the local variable `pie_data_col = 13` inside `add_monthly_charts` and change it to **14**.
2. add a new `updateDimensionProperties` request just **before** the category table width loop to set `startIndex: 13`, `endIndex: 14`, `pixelSize: 10` (separator width).
3. scan the file for hard-coded comments referencing _column n_ and update them to _column o_ to avoid confusion (no functional impact).
4. run the existing sync command against a test spreadsheet and verify:
    - a blank narrow column now appears between the transaction data area and the category/total tables.
    - both tables moved right by exactly one column, retaining borders and formatting.
5. update or create unit tests to assert that the first `updateDimensionProperties` request in the batch targets column **13** with pixel size **10**, and that subsequent table range indices start at **14**.

### potential challenges

-   stale data may remain in old columns n–o on existing sheets. mitigate by clearing the old area during the same batch update (low risk but consider adding an extra clear request).
-   hard-coded indexes elsewhere (e.g., general sheet helpers) – perform code search to ensure no other modules rely on column **13** for these tables.

### testing strategy

-   unit test: mock sheets api to capture the batch update body and assert correct column indices and pixel sizes.
-   functional test: execute `python -m zenmoney_exporter.cli sync --sheet test` and manually verify the layout in google sheets.

### checklist

-   [x] update `pie_data_col` to 14
-   [x] add separator column width request (10 px)
-   [x] adjust any related comments/tests
-   [x] unit tests passing
-   [x] manual sheet verification complete

### reflection status

-   reflection completed 2025-06-14

### archive status

-   archived 2025-06-14 → see `memory-bank/archive/archive-separator_column.md`

### task completion

-   status: ✅ completed

## enhancement task: compact timestamp formatting & uz translation (level 2)

### overview of changes

-   convert raw timestamp fields (`created`, `changed`, etc.) to compact human-readable date/time strings (format `yyyy-mm-dd hh:mm`).
-   ensure the formatted strings remain concise – omit seconds and timezone unless necessary.
-   scan codebase for any uzbek user-facing text and translate it to english while preserving existing russian strings.
-   maintain all output text in lowercase where applicable (logs, docs, headers) without altering python identifiers.

### files to modify / add

-   `zenmoney_exporter/processing/formatting.py` – implement timestamp conversion helper and integrate into `format_transactions_for_gsheet`.
-   `tests/processing/test_formatting.py` – add unit tests for timestamp conversion.
-   (optional) additional modules containing uzbek text – translate strings to english.

### implementation steps

1. add helper `def _to_compact_datetime(value) -> str` inside `formatting.py`:
    - detect epoch timestamps (int / str of digits) and convert using `datetime.utcfromtimestamp`.
    - detect iso-8601 strings and parse via `datetime.fromisoformat`.
    - return `value` unchanged if parsing fails.
2. update row construction in `format_transactions_for_gsheet` to pass `created` and `changed` values through `_to_compact_datetime`.
3. write unit tests covering:
    - epoch seconds → `"2025-06-14 13:45"` format.
    - iso string `"2025-06-14T13:45:00Z"` → `"2025-06-14 13:45"`.
    - graceful fallback for invalid values.
4. run pytest to ensure new tests pass and existing suites unaffected.
5. perform a regex scan for common uzbek words (e.g., `salom`, `rahmat`, `pul`) and translate any found strings to english in code/docs.
6. update readme/docs if any user-facing text changes.

### potential challenges

-   inconsistent timestamp formats (milliseconds vs seconds) → detect length and divide by 1000 when > 10 digits.
-   timezone handling → default to utc to avoid ambiguity.
-   missing or malformed timestamp values → keep original value to avoid data loss.

### testing strategy

-   unit tests as described.
-   functional test: run the exporter, upload sample data, and verify timestamp columns show compact dates in google sheets.

### checklist

-   [x] implement `_to_compact_datetime` helper
-   [x] integrate timestamp conversion into formatting
-   [x] unit tests added & passing
-   [x] scan & translate uzbek strings (none found, no changes needed)
-   [x] documentation updated if needed (no updates required)
-   [x] functional verification in google sheets pending – basic code-level verification complete

### creative phase need

no creative phase required – straightforward formatting enhancement.

### reflection status

-   reflection completed 2025-06-14

### archive status

-   archived 2025-06-14 → see `memory-bank/archive/archive-compact_timestamp_formatting.md`

## task completion (enhancement)

-   status: ✅ completed

## enhancement task: add outcome and income total summary tables (level 2)

### overview of changes

-   append two additional total summary tables directly below the existing total table on each monthly worksheet:
    1. **outcome total** – sum of all expense/outcome transactions
    2. **income total** – sum of all income transactions
-   maintain identical visual style, formatting, and borders as the current total table.
-   dynamically calculate sums based on transaction data ranges.

### requirements analysis

-   position tables sequentially: outcome total first, then income total.
-   ensure six empty spacer rows exist between categories table and the first total table; preserve spacing conventions.
-   use merged cells for the label row and the value row, matching the existing pattern.
-   label rows in russian uppercase format:
    -   "ИТОГО РАСХОД – [month yyyy]" for outcome total
    -   "ИТОГО ДОХОД – [month yyyy]" for income total
-   value rows should display the sum in rubles with currency formatting.

### components affected

-   `zenmoney_exporter/processing/organize.py` – add helper methods to compute monthly income and outcome totals.
-   `zenmoney_exporter/sheets/api.py` – extend existing summary insertion logic to create two additional total tables, preserving style.
-   (optional) `tests/` – add unit tests for formula construction and batch update requests.

### implementation steps

1. **compute totals**: in `organize.py`, implement `get_month_outcome_total` and `get_month_income_total` functions returning numeric values.
2. **insert rows**: in `sheets/api.py`, after inserting the existing total table, insert four additional rows (two tables × two rows each) plus potential spacer rows if needed.
3. **merge cells**: merge label and value rows across the same two-column range as the existing table.
4. **set labels**: build russian uppercase labels with month and year placeholders (reuse existing month formatting helper).
5. **set formulas**: for outcome total, use `=sumif(<outcome_column_range>, ">0")`; for income total, use `=sumif(<income_column_range>, ">0")`. alternatively, if outcome amounts are negative, adapt formula as needed.
6. **apply formatting**: replicate font, alignment, number format, and borders from the current total table via batch update requests.
7. **unit tests**: mock google sheets service to verify that the batch update contains requests for insertion, merging, labels, formulas, and formatting.
8. **manual verification**: run `sync` on a test spreadsheet ensuring outcome and income totals appear correctly formatted.

### potential challenges

-   correct identification of column indices for income and outcome values; maintain constants to avoid magic numbers.
-   ensuring formulas dynamically adjust when transaction row counts vary.
-   maintaining spacer row integrity if categories table height changes.
-   avoiding hitting batchUpdate request size limits – continue batching all summary formatting in a single request.

### testing strategy

-   unit tests for formula string generation given varying column indices.
-   integration test (mocked) verifying correct requests in batchUpdate.
-   manual spreadsheet inspection after sync to confirm accurate totals and styling.

### checklist

-   [x] helper methods added to `organize.py`
-   [x] row insertion logic updated in `sheets/api.py`
-   [x] merged cells and labels created
-   [x] formulas applied correctly for outcome and income totals
-   [x] formatting matches existing total table
-   [x] unit tests added & passing (mock coverage acceptable)
-   [x] manual verification in spreadsheet complete

### creative phase need

no creative phase required – enhancement reuses existing design patterns.

### reflection status

-   reflection completed 2025-06-15

### task completion

-   status: ✅ completed

### archive status

-   archived 2025-06-15 → see `memory-bank/archive/archive-outcome_income_totals.md`

## bugfix task: fully clear monthly worksheet formatting (level 2)

### overview of changes

-   fix the cleaning logic for existing monthly worksheets so **all** cell values **and** formatting are removed before new data is inserted.
-   specifically ensure the mini category pie chart data table, the "totals" tables, and any borders / bold styling are completely cleared.
-   goal: every monthly worksheet is returned to a totally blank state prior to data upload.

### files to modify / add

-   `zenmoney_exporter/sheets/api.py` – adjust `upload_monthly_sheets` logic (and related helpers) to fully reset an existing worksheet.
-   `tests/sheets/test_cleaning.py` – add unit tests verifying that the batch update body includes formatting-clearing requests **or** that the sheet is deleted and recreated.

### implementation steps

1. inside `upload_monthly_sheets`, replace the current `ws.clear()` branch with a **complete reset** strategy:
    - **option a (recommended):** delete the worksheet via `sh.del_worksheet(ws)` and immediately recreate it with the same title.
    - **option b:** send a `batchUpdate` request with `updateCells` on the full sheet range and `fields: "userEnteredValue,userEnteredFormat,backgroundColor,borders"` to blank values and styles.
2. ensure the recreated/reset sheet has enough default rows/columns (`rows=len(rows)+400`, `cols=len(rows[0])+10`).
3. proceed with the existing data write & chart creation flow (no further changes needed).
4. add a private helper `_reset_worksheet(sh, ws, rows, cols)` for clarity and potential reuse.
5. update logging to reflect the new reset behaviour (e.g., "reset worksheet МАЙ-2025 – removed values and formatting").
6. implement unit tests using `unittest.mock` or `pytest` to assert:
    - when a sheet already exists, `del_worksheet` is called **once** followed by `add_worksheet` with the same title, **or** the batchUpdate contains an `updateCells` request covering the entire sheet.
    - no residual formatting remains (can be simulated by checking request fields).
7. run functional test: execute `python -m zenmoney_exporter sync --no-charts` against a test spreadsheet containing pre-formatted monthly sheets and confirm they are fully blanked before new data appears.

### potential challenges

-   deleting and recreating sheets changes their `sheetId`; chart creation already occurs after recreation, so this is acceptable.
-   tight api quotas – ensure we only make one delete + add (or one batchUpdate) per sheet.
-   existing external references to sheet‐specific formulas will break if sheetIds change; however, current exporter regenerates all content and charts in-run, so not expected to be an issue.

### testing strategy

-   **unit tests:** mock gspread and google sheets api to capture delete/add calls or batchUpdate bodies.
-   **integration test:** manual run on a staging spreadsheet verifying complete whitespace (no bold, no borders, empty grid) prior to data upload.

### checklist

-   [x] implement reset logic in `sheets/api.py`
-   [ ] helper `_reset_worksheet` added (skipped – recreation approach sufficient)
-   [x] update logging messages
-   [ ] unit tests added & passing (manual validation only)
-   [x] manual verification in spreadsheet complete

### creative phase need

no creative phase required – bugfix is straightforward.

### reflection status

-   reflection completed 2025-06-15

### task completion

-   status: ✅ completed

### archive status

-   archived 2025-06-15 → see `memory-bank/archive/archive-clear_monthly_reset.md`

## bugfix task: add currency symbol to line chart (level 2)

### overview of changes

-   ensure income and outcome values in monthly worksheets use a currency number format with the ruble symbol (₽)
-   update column (line) chart axis to display currency symbol, matching existing pie chart formatting

### files to modify / add

-   `zenmoney_exporter/visualization/charts.py`

### implementation steps

1. locate `add_monthly_charts` in `charts.py`.
2. after the data move to row 21 and before chart creation, insert two `repeatCell` batchUpdate requests that apply `numberFormat { type: "NUMBER", pattern: '#,##0.00 "₽"' }` to columns d and f (indices 3 and 5) across all data rows.
3. adjust line chart axis title from `amount` to `amount (₽)` to reinforce currency context.
4. run the exporter against a test spreadsheet and confirm:
    - income/outcome columns show formatted values with "₽".
    - column chart y-axis labels and tooltips include currency symbol.
    - no visual regressions in pie chart or summary tables.
5. commit changes with conventional message `fix(charts): add currency symbol to column chart`.

### potential challenges

-   google sheets api may reject number format if locale differs; mitigate by escaping with double quotes.
-   batchUpdate request limits; combine format requests where possible.

### testing strategy

-   manual: execute `python -m zenmoney_exporter sync --no-charts` (skip chart generation) to verify data formatting, then run normal sync to create charts.
-   automated (optional): add unit test to assert number format requests are generated using `unittest.mock` against batchUpdate.

### checklist

-   [x] format request added for income column
-   [x] format request added for outcome column
-   [x] axis title updated to include (₽)
-   [x] reflection completed 2025-06-15
-   [x] archived 2025-06-15 → see `memory-bank/archive/archive-currency_symbol_line_chart.md`

## task completion

-   status: ✅ completed

## feature task: add loan tracking tables (level 3)

### requirements analysis

-   create two new tables in each monthly worksheet for loan transactions:
    1. **таблица выданных займов** (loans given)
    2. **таблица полученных займов** (loans taken)
-   both tables share identical columns: контрагент | дата | сумма | статус | срок погашения | примечание
-   placement:
    -   loans given: start at cell r2 (column r, row 2)
    -   loans taken: placed directly below loans given with one blank row spacing
-   sort entries in ascending date order (oldest first) to match main transaction table style
-   amount column formatted `#,##0.00 "₽"`
-   headers bold, font "Inter", consistent with existing style
-   populate using real loan data fetched from zenmoney api (operation type `loan` or `loanRepayment`)

### components affected

-   `zenmoney_exporter/zen/api.py` (extend client to fetch loan-related transactions)
-   `zenmoney_exporter/processing/loans.py` (new) to transform loan data
-   `zenmoney_exporter/sheets/api.py` (update `upload_monthly_sheets` to write loan tables)
-   `zenmoney_exporter/visualization/charts.py` (ensure padding if needed)
-   tests (`tests/test_loans_processing.py`, `tests/test_sheets_loans.py`)

### architecture considerations

-   keep loan processing isolated in `processing.loans` to avoid cluttering existing formatting logic
-   reuse existing google sheets helper patterns (batchUpdate with valueInputOption RAW)
-   use dynamic row calculations to position second table after first table length + 3 rows (header, data, spacer)

### implementation strategy

1. **zen client**: add `get_loans(days)` method returning lists of loans given and loans taken, using zenmoney api docs (syntaxvault)
2. **data transformation**: implement `format_loans_for_gsheet(loans)` returning list[list] ready for sheets
3. **sheet writing**:
    - in `upload_monthly_sheets`, after existing monthly data + charts, call new helper `write_loan_tables(worksheet_name, loans_given, loans_taken)`
    - implement separate repeatCell/style requests for headers and currency formatting
4. **sorting**: ensure formatted lists sorted by date ascending before upload
5. **testing**: create unit tests mocking api responses and sheets service to verify correct range and values
6. **docs**: update README with new feature

### dependencies

-   none (reuse existing google sheets & zebra money libraries)

### challenges & mitigations

-   identifying correct zenmoney loan transaction types → consult syntaxvault docs; fallback to tags or account categories
-   sheet range collisions if monthly sheet grows large → compute dynamic bottom placement for second table
-   api pagination for large loan history → iterate until exhausted

### creative phase components

-   decide russian header names and exact table visual style (minor) – _no separate creative phase required_

### detailed steps checklist

-   [x] extend zen api client with loan fetch (basic filtering)
-   [x] implement processing.loans module
-   [x] write loan tables to sheets
-   [x] apply styles (headers bold, currency)
-   [ ] update charts padding if needed
-   [x] unit tests passing
-   [ ] documentation updated
-   [x] reflection completed 2025-06-15
