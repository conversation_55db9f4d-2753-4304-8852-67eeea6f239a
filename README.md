# zenmoney to google sheets exporter

simple tool to export transactions from zenmoney to google sheets. like pipe connecting your money flow to spreadsheet tank.

## setup

1. install requirements:

```bash
uv pip install -r requirements.txt  # pip will also work if uv not available
```

2. create configuration file `credentials/config.yaml` (or point to a custom path via the `ZENMONEY_EXPORTER_CONFIG` environment variable). use the template below and adjust values:

```yaml
zenmoney:
    api_key: "<your_zenmoney_api_token>"

google_sheets:
    credentials_path: "credentials/account.json" # path to service account json
    spreadsheet_key: "<your_spreadsheet_id>"
    worksheet_name: "ГЛАВНЫЙ"

settings:
    days_to_fetch: 30
    log_level: "INFO"
```

3. setup credentials:
    - get zenmoney api key from https://zerymoney.ru/a/token/
    - create google service account and download json key file to `credentials/` folder
    - share your google sheet with the service account email (found in json file)

## usage (new cli)

install dependencies:

```bash
uv pip install -r requirements.txt  # or pip if uv not available
```

run sync to fetch and upload transactions:

```bash
python -m zenmoney_exporter sync
```

available commands:

| command | description                                                             |
| ------- | ----------------------------------------------------------------------- |
| `sync`  | fetch transactions from zenmoney and upload to google sheets            |
| `clean` | clear data in general sheet and delete all monthly worksheets           |
| `stats` | display total income/outcome for configured timeframe without uploading |

additional options:

| command | option        | description                                  |
| ------- | ------------- | -------------------------------------------- |
| `sync`  | `--no-charts` | skip chart generation for monthly worksheets |

for help:

```bash
python -m zenmoney_exporter --help
```

## development & testing

create virtual environment (optional) and install dev dependencies:

```bash
uv pip install -r requirements.txt
```

run unit tests:

```bash
pytest -q
```

project structure:

```
zenmoney_exporter/
  config.py
  utils/
  zen/
  sheets/
  processing/
  visualization/
  cli.py
  __main__.py
```

all features including monthly charts have been successfully migrated to the new package structure. legacy `main.py` remains for reference only.

## configuration

all settings in `config.yaml`:

-   **zenmoney.api_key**: your zenmoney api token
-   **google_sheets.credentials_path**: path to google service account json
-   **google_sheets.spreadsheet_key**: id of your google spreadsheet
-   **google_sheets.worksheet_name**: name of worksheet (will create if not exists)
-   **settings.days_to_fetch**: how many days of transactions to get
-   **settings.log_level**: logging level (INFO, DEBUG, etc.)

code is like pipe - when work, is beautiful. when break, is disaster.

## features

-   fetches transactions from zenmoney api
-   formats and uploads data to google sheets with **automatic date sorting (oldest first)**
-   uses service account authentication for google sheets
-   handles errors gracefully
-   organizes transactions by month into separate worksheets
-   creates detailed monthly charts for income/outcome and expense categories
-   retrieves all historical tags to properly categorize transactions
-   extracts loans and debts, attaching **loans given** and **loans taken** tables to every monthly worksheet for easy tracking

## tag handling

the application now properly retrieves and displays transaction categories:

-   fetches all historical tags from zenmoney, not just recent ones
-   ensures proper mapping of transaction categories in google sheets
-   avoids "unknown_tag_xxxx" placeholders in the spreadsheet
-   enables more accurate tag-based analysis and charts

## monthly transaction organization

transactions are now automatically organized by month into separate worksheets:

-   each month gets its own worksheet named with Russian month names like `МАЙ-2025` or `АПРЕЛЬ-2025`
-   transactions with unexpected date formats are placed in `unknown` sheet
-   all worksheets maintain the same column structure and formatting
-   **transactions are sorted by date with oldest transactions first** within each month
-   makes it easier to analyze spending patterns by month

## monthly charts

each monthly transaction worksheet now includes two helpful charts positioned above the transaction table:

-   **income vs outcome line chart**: visualizes daily income and expense trends throughout the month
-   **expense categories pie chart**: shows the top 5 expense categories and their proportions
-   charts are positioned at the top of each monthly worksheet with appropriate padding from the data table
-   pie chart automatically calculates total expenses by tag category
-   income line shown in green, expenses in red for easy tracking
