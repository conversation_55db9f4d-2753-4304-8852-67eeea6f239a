from unittest.mock import MagicMock, patch

from zenmoney_exporter.visualization.charts import add_monthly_charts


def test_add_monthly_charts_insufficient_data():
    """test that chart creation is skipped when insufficient data is provided."""
    # setup
    service = MagicMock()
    spreadsheet_key = "test_key"
    worksheet_name = "test_sheet"
    sheet_id = 123
    data = []  # empty data

    # execute
    add_monthly_charts(service, spreadsheet_key, worksheet_name, sheet_id, data)

    # verify
    service.spreadsheets.assert_not_called()


def test_add_monthly_charts_request_structure():
    """test that chart creation builds appropriate request structure."""
    # setup
    service = MagicMock()
    service.spreadsheets().batchUpdate.return_value.execute.return_value = {}
    service.spreadsheets().values().clear.return_value.execute.return_value = {}
    service.spreadsheets().values().update.return_value.execute.return_value = {}

    spreadsheet_key = "test_key"
    worksheet_name = "test_sheet"
    sheet_id = 123

    # sample data with header row and 3 data rows
    data = [
        [
            "date",
            "type",
            "income_acc",
            "income",
            "outcome_acc",
            "outcome",
            "currency",
            "payee",
            "comment",
            "tags",
        ],
        ["2025-05-01", "income", "card", "1000", "", "", "руб", "salary", "", "salary"],
        [
            "2025-05-02",
            "outcome",
            "",
            "",
            "card",
            "500",
            "руб",
            "shop",
            "",
            "groceries",
        ],
        [
            "2025-05-03",
            "outcome",
            "",
            "",
            "card",
            "300",
            "руб",
            "cafe",
            "",
            "restaurant, food",
        ],
    ]

    # execute with patch to avoid actual api calls
    with patch("zenmoney_exporter.visualization.charts.logger"):
        add_monthly_charts(service, spreadsheet_key, worksheet_name, sheet_id, data)

    # verify
    # check that padding request was made
    service.spreadsheets().batchUpdate.assert_called()

    # at least one batch update should contain a charts request
    charts_request_made = False
    for call in service.spreadsheets().batchUpdate.call_args_list:
        args, kwargs = call
        body = kwargs.get("body", {})
        if "requests" in body and any("addChart" in req for req in body["requests"]):
            charts_request_made = True
            break

    assert charts_request_made, "No chart request was created"
