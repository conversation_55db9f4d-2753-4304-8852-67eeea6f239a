# active context

## current mode

-   active mode: van

## next task

-   pending

## van mode detection results

-   platform: windows 10.0.26100
-   python version: 3.13.3
-   package manager: uv 0.7.9
-   memory bank: exists and properly structured
-   project structure: module-based hierarchy with zenmoney_exporter package
-   all dependencies installed: zenmoney, gspread, loguru, pyyaml, googleapiclient, pytest
-   credentials: config.yaml contains proper api keys and service account path
