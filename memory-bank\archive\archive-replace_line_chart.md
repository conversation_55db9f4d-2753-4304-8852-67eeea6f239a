# archive – replace line chart with column chart

**date archived:** 2025-06-14
**task level:** 2 (simple enhancement)

## summary

converted the monthly income/outcome visualization from a line chart to a column (bar) chart in google sheets, ensuring identical data source, colors, and interactive features. unified chart typography by setting the inter font for titles and axis labels, resolving api schema issues encountered during implementation.

## key files modified

-   `zenmoney_exporter/visualization/charts.py`
    -   changed `basicChart.chartType` from `LINE` to `COLUMN`.
    -   added `titleTextFormat.fontFamily` and axis `format.fontFamily` set to `inter`.
    -   updated docstrings, log messages, and comments accordingly.
-   `memory-bank/tasks.md` – task definition, checklist, reflection, archive status.
-   `memory-bank/reflection/reflection-replace_line_chart.md` – reflection document.

## implementation highlights

1. identified active line chart implementation in visualization module.
2. updated chart spec; legacy code left untouched as it's unused.
3. initial attempts to style axis failed due to incorrect field names; fixed by using `format.fontFamily`.
4. verified functional sync; charts rendered correctly with column bars and inter font.
5. checklist completed and reflection documented.

## validation

-   functional test ran on test spreadsheet id `1se7sf6jgw...` – charts displayed as expected without errors.
-   unit tests updated to assert `"COLUMN"` presence in generated requests.
-   no regression in pie chart creation or data upload.

## future considerations

-   abstract chart generation utilities for consistent style props.
-   automate request schema validation before api calls.

---

archived by automated memory bank process.
