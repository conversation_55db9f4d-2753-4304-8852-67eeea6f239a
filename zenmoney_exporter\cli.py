from __future__ import annotations

import argparse
import sys

from loguru import logger

from .utils import get_logger
from .config import load_config

# legacy main script no longer required; kept optional for backward compatibility
try:
    from importlib import import_module

    _legacy_main = import_module("main")
except ModuleNotFoundError:
    _legacy_main = None

# new internal modules
from .zen import ZenMoneyClient
from .processing import format_transactions_for_gsheet, organize_transactions_by_month
from .sheets import GoogleSheetsClient
from .processing.loans import format_loans_for_gsheet, organize_loans_by_month


def command_sync(args):
    """sync zenmoney transactions to google sheets using new internal modules."""
    # load configuration
    config = load_config()
    if not config:
        logger.error("could not load configuration – aborting")
        sys.exit(1)

    # reconfigure logger level from config
    get_logger(config["settings"]["log_level"])

    # fetch transactions via new zen client
    days_to_fetch = config["settings"]["days_to_fetch"]
    zen_client = ZenMoneyClient(config["zenmoney"]["api_key"])
    transactions, accounts_obj, tags = zen_client.get_transactions(days_to_fetch)

    if transactions is None:
        logger.error("failed to fetch transactions – exiting")
        sys.exit(1)

    # derive titles dict for existing formatting helper
    accounts_titles = (
        {k: v.title if hasattr(v, "title") else str(v) for k, v in accounts_obj.items()}
        if accounts_obj
        else {}
    )
    sheet_data = format_transactions_for_gsheet(transactions, accounts_titles, tags)

    # instantiate sheets client
    sheets_client = GoogleSheetsClient(config["google_sheets"]["credentials_path"])

    if _legacy_main:
        # fallback: use legacy upload if available (should not happen in tests)
        sheets_client.upload_general_sheet(
            sheet_data,
            config["google_sheets"]["spreadsheet_key"],
            config["google_sheets"]["worksheet_name"],
        )
    else:
        sheets_client.upload_general_sheet(
            sheet_data,
            config["google_sheets"]["spreadsheet_key"],
            config["google_sheets"]["worksheet_name"],
        )

    monthly_data = organize_transactions_by_month(sheet_data)

    # process loan transactions and organize by month
    loans_given_rows, loans_taken_rows = format_loans_for_gsheet(
        transactions, accounts_obj
    )
    loans_monthly = organize_loans_by_month(loans_given_rows, loans_taken_rows)

    sheets_client.upload_monthly_sheets(
        monthly_data,
        config["google_sheets"]["spreadsheet_key"],
        not args.no_charts,
        loans_monthly,
    )

    logger.info("sync complete")


def command_clean(args):
    """remove data from general worksheet and delete monthly worksheets."""
    config = load_config()
    if not config:
        logger.error("could not load configuration – aborting clean")
        sys.exit(1)

    sheets_client = GoogleSheetsClient(config["google_sheets"]["credentials_path"])
    sheets_client.clear_general_sheet(
        config["google_sheets"]["spreadsheet_key"],
        config["google_sheets"]["worksheet_name"],
    )
    sheets_client.clear_monthly_sheets(config["google_sheets"]["spreadsheet_key"])
    logger.info("clean complete")


def command_stats(args):
    """print simple total income/outcome stats for configured period without uploading."""
    config = load_config()
    if not config:
        logger.error("could not load configuration – aborting stats")
        sys.exit(1)

    zen_client = ZenMoneyClient(config["zenmoney"]["api_key"])
    days_to_fetch = config["settings"]["days_to_fetch"]
    transactions, _accounts, _tags = zen_client.get_transactions(days_to_fetch)
    if transactions is None:
        logger.error("failed to fetch transactions – exiting")
        sys.exit(1)

    total_income = sum(getattr(t, "income", 0) for t in transactions) / 100.0
    total_outcome = sum(getattr(t, "outcome", 0) for t in transactions) / 100.0

    logger.info(f"total income for last {days_to_fetch} days: {total_income:.2f}")
    logger.info(f"total outcome for last {days_to_fetch} days: {total_outcome:.2f}")
    net = total_income - total_outcome
    logger.info(f"net flow: {net:.2f}")


def build_parser():
    parser = argparse.ArgumentParser(
        prog="zenmoney-exporter",
        description="sync zenmoney transactions to google sheets",
    )
    subparsers = parser.add_subparsers(dest="command", required=True)

    sync_parser = subparsers.add_parser(
        "sync", help="fetch transactions and update sheets"
    )
    sync_parser.set_defaults(func=command_sync)
    sync_parser.add_argument(
        "--no-charts", action="store_true", help="disable chart generation"
    )

    clean_parser = subparsers.add_parser("clean", help="clean old data from sheets")
    clean_parser.set_defaults(func=command_clean)

    stats_parser = subparsers.add_parser(
        "stats", help="generate statistics without upload"
    )
    stats_parser.set_defaults(func=command_stats)

    return parser


def main(argv: list[str] | None = None):
    get_logger()  # initialize global logger with default level
    parser = build_parser()
    args = parser.parse_args(argv)
    args.func(args)
