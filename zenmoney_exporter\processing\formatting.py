from __future__ import annotations

from typing import List, Dict, Any
from datetime import datetime

from loguru import logger


def _to_compact_datetime(value) -> str:
    """convert various timestamp formats to 'yyyy-mm-dd hh:mm' (utc, compact, lowercase)."""
    if value is None or value == "":
        return ""
    # epoch seconds or ms (int or str)
    try:
        if isinstance(value, int) or (isinstance(value, str) and value.isdigit()):
            v = int(value)
            # if ms, convert to seconds
            if v > 1e12:
                v = v // 1000
            dt = datetime.utcfromtimestamp(v)
            return dt.strftime("%Y-%m-%d %H:%M")
    except Exception:
        pass
    # iso-8601 string
    try:
        # handle 'Z' (utc) at end
        s = str(value).replace("Z", "")
        dt = datetime.fromisoformat(s)
        return dt.strftime("%Y-%m-%d %H:%M")
    except Exception:
        pass
    return str(value)


def format_transactions_for_gsheet(
    transactions, accounts: Dict[str, str], tags: Dict[str, str]
):
    """takes raw transactions from zenmoney and formats them for google sheets.

    this is adapted directly from the original implementation in `main.py` to preserve behavior.
    transactions are sorted by date with oldest first.
    """
    if not transactions:
        return []

    # sort transactions by date (oldest first)
    # assuming transactions have a 'date' attribute in YYYY-MM-DD format
    sorted_transactions = sorted(
        transactions,
        key=lambda t: getattr(t, "date", "") if getattr(t, "date", "") else "",
    )

    # russian header names for readability in the spreadsheet ui
    header_map = {
        "date": "дата",
        "type": "тип",
        "income_account": "счёт дохода",
        "income": "доход",
        "outcome_account": "счёт расхода",
        "outcome": "расход",
        "currency": "валюта",
        "payee": "получатель",
        "comment": "комментарий",
        "tags": "теги",
        # keep transaction_id in english; this column will be hidden later
        "transaction_id": "transaction_id",
        "created_at": "создано",
        "changed_at": "изменено",
    }

    # define the internal column order – do not change to avoid breaking downstream logic
    column_keys = [
        "date",
        "type",
        "income_account",
        "income",
        "outcome_account",
        "outcome",
        "currency",
        "payee",
        "comment",
        "tags",
        "transaction_id",
        "created_at",
        "changed_at",
    ]

    header = [header_map[key] for key in column_keys]
    formatted_data = [header]

    for t in sorted_transactions:
        if getattr(t, "deleted", False):
            continue

        tx_type = "unknown"
        if getattr(t, "income", 0) > 0 and getattr(t, "outcome", 0) == 0:
            tx_type = "income"
        elif getattr(t, "outcome", 0) > 0 and getattr(t, "income", 0) == 0:
            tx_type = "outcome"
        elif getattr(t, "income", 0) > 0 and getattr(t, "outcome", 0) > 0:
            tx_type = "transfer"
        elif getattr(t, "opIncome", 0) > 0 or getattr(t, "opOutcome", 0) > 0:
            tx_type = "currency_exchange"

        income_acc_name = accounts.get(getattr(t, "incomeAccount", None), "unknown")
        outcome_acc_name = accounts.get(getattr(t, "outcomeAccount", None), "unknown")

        raw_tag_ids = getattr(t, "tag", []) or []
        tag_titles = [
            tags.get(tag_id, f"unknown_tag_{tag_id}") for tag_id in raw_tag_ids
        ]
        tags_str = ", ".join(tag_titles)

        # currency mapping – map instrument codes to russian names
        instrument_code = getattr(t, "instrument", "RUB")
        code_to_russian = {
            "RUB": "руб",
            "USD": "доллар",
            "EUR": "евро",
        }
        currency_name = code_to_russian.get(instrument_code, instrument_code.lower())

        # convert amounts to rubles using rudimentary rate mapping (will be replaced with live rates)
        currency_rates = {"RUB": 1, "USD": 90.0, "EUR": 100.0}
        rate = currency_rates.get(instrument_code, 1)
        income_rub = getattr(t, "income", 0) / 100.0 * rate
        outcome_rub = getattr(t, "outcome", 0) / 100.0 * rate

        row = [
            getattr(t, "date", "unknown"),
            tx_type,
            income_acc_name,
            income_rub,
            outcome_acc_name,
            outcome_rub,
            currency_name,
            getattr(t, "payee", ""),
            getattr(t, "comment", ""),
            tags_str,
            getattr(t, "id", "unknown"),
            _to_compact_datetime(getattr(t, "created", "")),
            _to_compact_datetime(getattr(t, "changed", "")),
        ]
        formatted_data.append(row)

    logger.info(
        f"formatted {len(formatted_data) - 1} transactions for gsheet upload (sorted by date)"
    )
    return formatted_data
