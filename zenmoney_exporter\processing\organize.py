from __future__ import annotations

from collections import defaultdict
from typing import List, Dict

from loguru import logger


def format_month_to_russian(year_month: str) -> str:
    """convert YYYY-MM format to Russian month name format like МАЙ-2025 or АПРЕЛЬ-2025."""
    if not year_month or len(year_month) < 7:
        return "unknown"

    try:
        year, month = year_month.split("-")
        month_num = int(month)

        # Russian month names mapping
        russian_months = {
            1: "ЯНВАРЬ",
            2: "ФЕВРАЛЬ",
            3: "МАРТ",
            4: "АПРЕЛЬ",
            5: "МАЙ",
            6: "ИЮНЬ",
            7: "ИЮЛЬ",
            8: "АВГУСТ",
            9: "СЕНТЯБРЬ",
            10: "ОКТЯБРЬ",
            11: "НОЯБРЬ",
            12: "ДЕКАБРЬ",
        }

        if month_num in russian_months:
            return f"{russian_months[month_num]}-{year}"
        else:
            return f"unknown-{year}"

    except (ValueError, IndexError):
        return "unknown"


def organize_transactions_by_month(data: List[List[str]]):
    """organize transaction rows (already formatted) into dict keyed by Russian month names."""
    if not data:
        return {}

    header, *rows = data
    month_groups: Dict[str, List[List[str]]] = defaultdict(list)
    for row in rows:
        # row[0] is date in format yyyy-mm-dd or similar
        year_month = row[0][:7] if row[0] else "unknown"
        month_key = format_month_to_russian(year_month)
        month_groups[month_key].append(row)

    monthly_data: Dict[str, List[List[str]]] = {}
    for month, month_rows in month_groups.items():
        # sort transactions by date (oldest first)
        # row[0] contains date in YYYY-MM-DD format which sorts naturally
        sorted_rows = sorted(month_rows, key=lambda row: row[0] if row[0] else "")
        monthly_data[month] = [header, *sorted_rows]
        logger.info(
            f"grouped {len(sorted_rows)} rows under month {month} (sorted by date)"
        )

    return monthly_data


def calculate_monthly_total(month_rows: List[List[str]]) -> float:
    """calculate the total amount for a month's data.

    expects *month_rows* to be the list returned by ``organize_transactions_by_month``
    which includes the header row followed by transaction rows. the function sums
    the numeric values in the *income* (index 3) and *outcome* (index 5) columns
    of each transaction row and returns the total as a float.

    notes:
    - non-numeric or empty cells are treated as ``0``.
    - income and outcome values are assumed to be positive numbers already
      converted from integer minor units (kopeks) to float.
    - if ``month_rows`` is empty or contains only the header the result is ``0.0``.
    """

    if not month_rows or len(month_rows) <= 1:
        return 0.0

    # skip header (month_rows[0]) and iterate data rows
    total: float = 0.0
    for row in month_rows[1:]:
        try:
            income_val = float(row[3]) if len(row) > 3 and row[3] else 0.0
        except (ValueError, TypeError):
            income_val = 0.0

        try:
            outcome_val = float(row[5]) if len(row) > 5 and row[5] else 0.0
        except (ValueError, TypeError):
            outcome_val = 0.0

        total += income_val + outcome_val

    return total


def calculate_monthly_outcome_total(month_rows: list[list[str]]) -> float:
    """calculate the total outcome (expenses) for a month's data."""
    if not month_rows or len(month_rows) <= 1:
        return 0.0
    total: float = 0.0
    for row in month_rows[1:]:
        try:
            outcome_val = float(row[5]) if len(row) > 5 and row[5] else 0.0
        except (ValueError, TypeError):
            outcome_val = 0.0
        total += outcome_val
    return total


def calculate_monthly_income_total(month_rows: list[list[str]]) -> float:
    """calculate the total income for a month's data."""
    if not month_rows or len(month_rows) <= 1:
        return 0.0
    total: float = 0.0
    for row in month_rows[1:]:
        try:
            income_val = float(row[3]) if len(row) > 3 and row[3] else 0.0
        except (ValueError, TypeError):
            income_val = 0.0
        total += income_val
    return total
