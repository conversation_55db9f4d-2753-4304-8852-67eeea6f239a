import sys
import pathlib

sys.path.append(str(pathlib.Path(__file__).resolve().parents[1]))

import datetime
from types import SimpleNamespace

from zenmoney_exporter.processing import (
    format_transactions_for_gsheet,
    organize_transactions_by_month,
)
from zenmoney_exporter.processing.organize import format_month_to_russian


def _dummy_tx(**kwargs):
    defaults = {
        "date": "2025-06-12",
        "income": 10000,
        "outcome": 0,
        "incomeAccount": "acc1",
        "outcomeAccount": "acc2",
        "tag": ["tag1"],
        "id": "tx1",
        "created": int(datetime.datetime.now().timestamp()),
        "changed": int(datetime.datetime.now().timestamp()),
        "deleted": False,
    }
    defaults.update(kwargs)
    return SimpleNamespace(**defaults)


def test_format_transactions_single_row():
    tx = _dummy_tx()
    accounts = {"acc1": "bank", "acc2": "cash"}
    tags = {"tag1": "food"}

    data = format_transactions_for_gsheet([tx], accounts, tags)

    # header + one row
    assert len(data) == 2
    header = data[0]
    row = data[1]

    assert header[0] == "дата"
    assert row[0] == "2025-06-12"
    assert row[2] == "bank"
    assert row[9] == "food"


def test_format_month_to_russian():
    # Test various months
    assert format_month_to_russian("2025-01") == "ЯНВАРЬ-2025"
    assert format_month_to_russian("2025-05") == "МАЙ-2025"
    assert format_month_to_russian("2025-12") == "ДЕКАБРЬ-2025"

    # Test edge cases
    assert format_month_to_russian("") == "unknown"
    assert format_month_to_russian("invalid") == "unknown"
    assert format_month_to_russian("2025-13") == "unknown-2025"  # invalid month


def test_organize_transactions_by_month():
    tx1 = _dummy_tx(date="2025-05-31", id="a")
    tx2 = _dummy_tx(date="2025-06-01", id="b")
    accounts = {}
    tags = {}

    data = format_transactions_for_gsheet([tx1, tx2], accounts, tags)
    monthly = organize_transactions_by_month(data)

    # Now expecting Russian month names
    assert set(monthly.keys()) == {"МАЙ-2025", "ИЮНЬ-2025"}
    assert len(monthly["МАЙ-2025"]) == 2  # header + one row
    assert len(monthly["ИЮНЬ-2025"]) == 2


def test_transactions_sorted_by_date():
    # Test that transactions are sorted by date (oldest first)
    tx1 = _dummy_tx(date="2025-05-15", id="newer")
    tx2 = _dummy_tx(date="2025-05-10", id="older")
    tx3 = _dummy_tx(date="2025-05-20", id="newest")
    accounts = {}
    tags = {}

    # Pass transactions in random order
    data = format_transactions_for_gsheet([tx1, tx2, tx3], accounts, tags)

    # Check that they are sorted by date (oldest first)
    assert data[1][0] == "2025-05-10"  # oldest
    assert data[2][0] == "2025-05-15"  # middle
    assert data[3][0] == "2025-05-20"  # newest

    # Test monthly organization also sorts within months
    monthly = organize_transactions_by_month(data)
    may_data = monthly["МАЙ-2025"]

    # Skip header row, check data rows are sorted
    assert may_data[1][0] == "2025-05-10"  # oldest
    assert may_data[2][0] == "2025-05-15"  # middle
    assert may_data[3][0] == "2025-05-20"  # newest


def test_monthly_sheets_chronological_order():
    """Test that monthly sheets are processed in chronological order."""
    # Create transactions across multiple months in random order
    tx1 = _dummy_tx(date="2025-06-15", id="june")
    tx2 = _dummy_tx(date="2025-04-10", id="april")
    tx3 = _dummy_tx(date="2025-05-20", id="may")
    tx4 = _dummy_tx(date="2024-12-05", id="december")
    accounts = {}
    tags = {}

    data = format_transactions_for_gsheet([tx1, tx2, tx3, tx4], accounts, tags)
    monthly = organize_transactions_by_month(data)

    # Test the sorting logic directly without initializing GoogleSheetsClient
    def month_to_sort_key(month_name: str) -> tuple:
        """convert Russian month name like 'МАЙ-2025' to sortable tuple (year, month_num)."""
        if month_name == "unknown":
            return (9999, 99)  # put unknown at the end

        try:
            month_part, year_part = month_name.split("-")
            year = int(year_part)

            # Russian month names to numbers
            russian_months = {
                "ЯНВАРЬ": 1,
                "ФЕВРАЛЬ": 2,
                "МАРТ": 3,
                "АПРЕЛЬ": 4,
                "МАЙ": 5,
                "ИЮНЬ": 6,
                "ИЮЛЬ": 7,
                "АВГУСТ": 8,
                "СЕНТЯБРЬ": 9,
                "ОКТЯБРЬ": 10,
                "НОЯБРЬ": 11,
                "ДЕКАБРЬ": 12,
            }

            month_num = russian_months.get(month_part, 99)
            return (year, month_num)
        except (ValueError, IndexError):
            return (9999, 99)  # fallback for malformed names

    sorted_months = sorted(monthly.keys(), key=month_to_sort_key)

    # Should be in chronological order: December 2024, April 2025, May 2025, June 2025
    expected_order = ["ДЕКАБРЬ-2024", "АПРЕЛЬ-2025", "МАЙ-2025", "ИЮНЬ-2025"]
    assert sorted_months == expected_order


def test__to_compact_datetime_epoch_seconds():
    from zenmoney_exporter.processing.formatting import _to_compact_datetime

    # epoch seconds (utc)
    assert _to_compact_datetime(1750181100) == "2025-06-17 17:25"
    assert _to_compact_datetime("1750181100") == "2025-06-17 17:25"


def test__to_compact_datetime_epoch_milliseconds():
    from zenmoney_exporter.processing.formatting import _to_compact_datetime

    # epoch ms (utc)
    assert _to_compact_datetime(1750181100000) == "2025-06-17 17:25"
    assert _to_compact_datetime("1750181100000") == "2025-06-17 17:25"


def test__to_compact_datetime_iso8601():
    from zenmoney_exporter.processing.formatting import _to_compact_datetime

    # iso-8601 string
    assert _to_compact_datetime("2025-06-17T11:25:00Z") == "2025-06-17 11:25"
    assert _to_compact_datetime("2025-06-17T11:25:00") == "2025-06-17 11:25"


def test__to_compact_datetime_invalid():
    from zenmoney_exporter.processing.formatting import _to_compact_datetime

    # invalid input returns as-is
    assert _to_compact_datetime(None) == ""
    assert _to_compact_datetime("") == ""
    assert _to_compact_datetime("not-a-date") == "not-a-date"
