# archive: add monthly total summary table (level 3)

date archived: 2025-06-14

## overview

-   inserted monthly total summary table below category table with six blank rows of spacing
-   merged label and value rows across two columns at column n
-   applied dynamic formula to sum income (column D) and outcome (column F)
-   formatted total cell with ruble currency symbol ("₽")

## implementation summary

-   added helper `calculate_monthly_total` in `zenmoney_exporter/processing/organize.py`
-   updated `zenmoney_exporter/sheets/api.py` to apply global unmerge for first 200 rows and adjust hidden column indices
-   modified `zenmoney_exporter/visualization/charts.py`:
    -   shifted pie and summary areas to start at column n
    -   inserted `unmergeCells` requests for global and summary ranges
    -   merged cells for label and value rows and set label text and formula
    -   applied `#,##0.00 "₽"` number format and `Inter` font styling
-   updated tests in:
    -   `tests/test_processing.py`
    -   `tests/test_charts.py`

## results

-   all unit tests pass
-   manual sync verified summary table placement and correct totals with currency symbol

## lessons learned

-   google sheets api requires selecting entire merged regions to unmerge cells
-   centralizing layout constants simplifies cross-feature adjustments
-   careful JSON quoting is needed for formulas and number format patterns
