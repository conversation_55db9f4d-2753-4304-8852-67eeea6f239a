# style guide

## code style

### python standards

-   follow pep 8 guidelines for python code
-   use 4 spaces for indentation (not tabs)
-   maximum line length of 88 characters (black compatible)
-   use docstrings for functions and modules

### naming conventions

-   use `snake_case` for variable and function names
-   use `UPPER_CASE` for constants
-   use descriptive names that indicate purpose
-   prefer clarity over brevity in naming

### logging standards

-   all log messages must be in lowercase
-   use appropriate log levels:
    -   `debug`: detailed information for troubleshooting
    -   `info`: confirmation that things are working
    -   `warning`: something unexpected but non-critical
    -   `error`: functionality is affected, cannot proceed
-   include relevant context in log messages

## file organization

-   keep imports at the top of the file
-   group imports in the following order:
    1. standard library imports
    2. third-party imports
    3. local application imports
-   separate import groups with a blank line

## documentation

### comments

-   all comments must be in lowercase
-   use inline comments sparingly, only when necessary
-   explain "why" not "what" in comments
-   maintain the humorous pipe metaphors in comments

### docstrings

-   use """triple double quotes""" for docstrings
-   include a brief description of the function purpose
-   document parameters and return values
-   mention exceptions that might be raised

## error handling

-   use specific exception types where possible
-   provide meaningful error messages
-   handle exceptions at appropriate levels
-   avoid catching generic exceptions unless absolutely necessary

## examples

### good function example

```python
def format_transactions_for_gsheet(transactions, accounts, tags):
    """
    formats raw transaction data from zenmoney for google sheets.

    args:
        transactions: list of transaction objects from zenmoney
        accounts: dictionary mapping account ids to names
        tags: dictionary mapping tag ids to names

    returns:
        list of lists representing formatted data for sheets

    raises:
        TypeError: if transactions is not iterable
    """
    if not transactions:
        return []

    # build header row for sheet
    header = [
        "date", "type", "income_account", "income",
        "outcome_account", "outcome", "payee", "comment",
        "tags", "transaction_id"
    ]
    formatted_data = [header]

    # transform each transaction into row format
    for t in transactions:
        # skip deleted transactions
        if getattr(t, "deleted", False):
            continue

        # format transaction data
        row = [
            format_date(t.date),
            determine_transaction_type(t),
            # additional formatting...
        ]
        formatted_data.append(row)

    return formatted_data
```

### good logging example

```python
# good logging
try:
    api = Request(CONFIG["zenmoney"]["api_key"])
    logger.info("successfully authenticated with zenmoney")
except Exception as e:
    # we need to catch all exceptions here
    logger.error(f"failed to authenticate with zenmoney: {e}")
    return None
```

## code review checklist

-   [ ] follows pep 8 guidelines
-   [ ] includes appropriate docstrings
-   [ ] log messages are in lowercase
-   [ ] error handling is appropriate
-   [ ] comments explain "why" not "what"
-   [ ] follows established naming conventions
-   [ ] maintains consistent style with existing code
